use nova_core::completion::{CompletionClientDyn, CompletionRequest};
use nova_core::completion::Message;
use nova_core::simple_chat::Client;
use serde_json::json;
use tokio_stream::StreamExt;
use nova_core::one_or_many::OneOrMany;

#[tokio::main]
async fn main() {
    let client = Client::new(
        "sk-nHbjOecBzg9GKYTtD0B04eFb7f37494e8eD57097408a8172",
        "http://121.52.244.250:3000/v1/chat/completions",
    );
    let model = client.completion_model("qwen2.5-72b-instruct");

    // let resp = model
    //     .completion_request(Message::user("你好?"))
    //     .send()
    //     .await;
    // match resp {
    //     Ok(resp) => {
    //         println!("{}", json!(resp));
    //     }
    //     Err(e) => {
    //         println!("{:?}", e);
    //     }
    // }

    let resp = model.stream(CompletionRequest {
        preamble: None,
        tools: vec![],
        temperature: None,
        max_tokens: None,
        additional_params: None,
        chat_history: OneOrMany::one(Message::user("你好")),
    });
    let mut resp = resp.await.unwrap();
    let mut received_chunk = false;
    while let Some(chunk) = resp.next().await {
        received_chunk = true;
        match chunk {
            Ok(resp) => {
                println!("{}", json!(resp));
            }
            Err(e) => {
                println!("{:?}", e);
            }
        }
    }

}
