use pyo3::prelude::*;
use pyo3::types::IntoPyDict;

fn main() -> PyResult<()> {
    pyo3::prepare_freethreaded_python();

    Python::with_gil(|py| {
        let locals = [("a", 10), ("b", 20)].into_py_dict(py);
        let script = include_str!("test.py"); // 包含Python脚本内容
        let result = py.run(script, None, Some(locals))?;
        // let res: i32 = result.extract(py)?;
        println!("Result: {}", locals); // 输出结果应该是30
        Ok(())
    })
}