--[[
根据获取任务
--]]

--[[
根据任务ID获取任务数据

这个脚本用于根据任务ID获取任务的详细数据。由于任务数据是按执行器ID组织存储的，
需要先查找任务对应的执行器，然后从对应的数据存储中获取任务数据。

参数说明：
KEYS[1]: 任务关联执行器映射 (hash) - 存储任务ID到执行器ID的映射
KEYS[2]: 数据键模板 - 包含 "replace" 占位符的模板字符串，如 "{namespace}:replace:data"
ARGV[1]: 任务ID - 要获取的任务标识

返回值：
返回任务的详细数据，如果任务不存在则返回 nil
--]]

-- 根据任务ID获取关联的执行器ID
local job_worker_id = redis.call("hget", KEYS[1], ARGV[1])
local results = nil

-- 如果找到了关联的执行器
if job_worker_id then
  -- 构建数据键，将模板中的 "replace" 替换为实际的执行器ID
  local data_key = string.gsub(KEYS[2], "replace", job_worker_id)

  -- 获取任务的详细数据
  results = redis.call("hget", data_key, ARGV[1])
end

-- 返回获取到的任务数据
return results