--[[
工作者获取任务

这个脚本用于工作者从任务队列中获取待处理的任务。它确保任务的原子性分配，
防止多个工作者获取同一个任务，并维护任务的执行状态跟踪。

参数说明：
KEYS[1]: 活跃任务列表 (list) - 存储等待处理的任务ID队列 active
KEYS[2]: 任务数据哈希表 (hash) - 存储任务ID到任务数据的映射 data

ARGV[1]: 最大获取任务数 - 限制单次获取的任务数量

返回值：
返回获取到的任务数据数组，如果没有任务则返回空数组
--]]


-- 从活跃任务列表中获取指定数量的任务ID
-- 使用 lrange 获取列表头部的任务，实现FIFO队列
local job_ids = redis.call("lrange", KEYS[1], 0, ARGV[1] - 1)
-- 注意：使用 # 操作符替代已废弃的 table.getn 函数
local count = #job_ids
local results = {}

-- 如果获取到任务
if count > 0 then
  -- 从活跃任务列表中移除已分配的任务
  -- 使用 ltrim 删除列表头部的已分配任务
  redis.call("ltrim", KEYS[1], count, -1)

  -- 获取任务的详细数据
  -- 使用 hmget 批量获取任务数据，提高效率
  results = redis.call("hmget", KEYS[2], unpack(job_ids))
end
-- 返回获取到的任务数据
return results
