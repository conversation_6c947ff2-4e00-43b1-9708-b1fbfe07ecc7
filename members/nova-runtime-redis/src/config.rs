use std::time::Duration;

use chrono::Local;


/// 任务关联执行器
const JOBS_WORKER_HASH: &str = "{queue}:job:";

/// 运行时队列
const ACTIVE_JOBS_LIST: &str = "{queue}::{worker_id}:active";

/// 任务数据
const JOB_DATA_HASH: &str = "{queue}::{worker_id}:data";

/// 近一小时完成任务统计
const REV_JOBS_SET: &str = "{queue}::mtr::{worker_id}::{near_hour}:rev";

/// 近一小时完成任务统计
const DONE_JOBS_SET: &str = "{queue}::mtr::{worker_id}::{near_hour}:done";

/// 近一小时失败任务统计
const FAILED_JOBS_SET: &str = "{queue}::mtr::{worker_id}::{near_hour}:failed";

/// 配置信息
#[derive(C<PERSON>, Debug)]
pub struct Config {
    pub poll_interval: Duration,
    pub buffer_size: usize,
    pub keep_alive: Duration,
    pub namespace: String,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            poll_interval: Duration::from_millis(100),
            buffer_size: 10,
            keep_alive: Duration::from_secs(30),
            namespace: String::from("nr"),
        }
    }
}

impl Config {
    /// Get the interval of polling
    pub fn get_poll_interval(&self) -> &Duration {
        &self.poll_interval
    }

    /// Get the number of jobs to fetch
    pub fn get_buffer_size(&self) -> usize {
        self.buffer_size
    }

    /// get the keep live rate
    pub fn get_keep_alive(&self) -> &Duration {
        &self.keep_alive
    }

    /// get the namespace
    pub fn get_namespace(&self) -> &String {
        &self.namespace
    }

    /// get the poll interval
    pub fn set_poll_interval(mut self, poll_interval: Duration) -> Self {
        self.poll_interval = poll_interval;
        self
    }

    /// set the buffer setting
    pub fn set_buffer_size(mut self, buffer_size: usize) -> Self {
        self.buffer_size = buffer_size;
        self
    }

    /// set the keep-alive setting
    pub fn set_keep_alive(mut self, keep_alive: Duration) -> Self {
        self.keep_alive = keep_alive;
        self
    }

    /// 全局前缀
    pub fn set_namespace(mut self, namespace: &str) -> Self {
        self.namespace = namespace.to_string();
        self
    }

    /// 活动任务列表 active
    pub fn active_jobs_list(&self, worker_id: &str) -> String {
        ACTIVE_JOBS_LIST.replace("{queue}", &self.namespace).replace("{worker_id}", worker_id)
    }

    /// 下发任务数据
    pub fn rev_jobs_set(&self, worker_id: &str) -> String {
        let near_hour = format!("{}", Local::now().format("%Y%m%d%H"));
        REV_JOBS_SET
            .replace("{queue}", &self.namespace)
            .replace("{worker_id}", worker_id)
            .replace("{near_hour}", &near_hour)
    }

    /// 完成任务数据
    pub fn done_jobs_set(&self, worker_id: &str) -> String {
        let near_hour = format!("{}", Local::now().format("%Y%m%d%H"));
        DONE_JOBS_SET
            .replace("{queue}", &self.namespace)
            .replace("{worker_id}", worker_id)
            .replace("{near_hour}", &near_hour)
    }

    /// 失败任务数据
    pub fn failed_jobs_set(&self, worker_id: &str) -> String {
        let near_hour = format!("{}", Local::now().format("%Y%m%d%H"));
        FAILED_JOBS_SET
            .replace("{queue}", &self.namespace)
            .replace("{worker_id}", worker_id)
            .replace("{near_hour}", &near_hour)
    }

    /// 任务数据
    pub fn job_data_hash(&self, worker_id: &str) -> String {
        JOB_DATA_HASH.replace("{queue}", &self.namespace).replace("{worker_id}", worker_id)
    }

    /// 任务数据
    pub fn job_worker_id(&self) -> String {
        JOBS_WORKER_HASH.replace("{queue}", &self.namespace)
    }
}
