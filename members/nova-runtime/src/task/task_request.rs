use crate::task::TaskId;
use crate::task::Extensions;
use serde::{Deserialize, Serialize};

#[non_exhaustive]
#[derive(Serialize, Debug, Deserialize, Clone, Default)]
pub struct Parts<Ctx> {
    /// 任务ID
    pub task_id: TaskId,

    /// 扩展数据，不存储在任务队列中
    #[serde(skip)]
    pub data: Extensions,

    /// 上下文数据，会存储在任务队列中
    pub context: Ctx,
}

#[derive(Serialize, Debug, Deserialize, Clone, Default)]
pub struct TaskRequest<Args, Ctx> {
    /// 任务参数
    pub args: Args,
    /// 参数额外数据
    pub parts: Parts<Ctx>,
}


impl<T, Ctx> TaskRequest<T, Ctx> {
    /// 创建一个新的任务请求
    pub fn new(args: T) -> Self
    where
        Ctx: Default,
    {
        Self::new_with_data(args, Extensions::default(), Ctx::default())
    }

    /// 创建一个新的任务请求携带额外数据
    pub fn new_with_parts(args: T, parts: Parts<Ctx>) -> Self {
        Self { args, parts }
    }

    /// 创建一个新的任务请求携带上下文
    pub fn new_with_ctx(req: T, ctx: Ctx) -> Self {
        Self {
            args: req,
            parts: Parts {
                context: ctx,
                task_id: Default::default(),
                data: Default::default(),
            },
        }
    }

    /// Creates a request with data and context provided
    pub fn new_with_data(req: T, data: Extensions, ctx: Ctx) -> Self {
        Self {
            args: req,
            parts: Parts {
                context: ctx,
                task_id: Default::default(),
                data,
            },
        }
    }

    /// Take the parts
    pub fn take_parts(self) -> (T, Parts<Ctx>) {
        (self.args, self.parts)
    }
}



impl<T, Ctx> std::ops::Deref for TaskRequest<T, Ctx> {
    type Target = Extensions;
    fn deref(&self) -> &Self::Target {
        &self.parts.data
    }
}

impl<T, Ctx> std::ops::DerefMut for TaskRequest<T, Ctx> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.parts.data
    }
}
