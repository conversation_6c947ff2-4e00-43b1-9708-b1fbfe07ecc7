use std::fmt;
use std::pin::Pin;
use std::task::Poll;

use futures::FutureExt;
use futures::StreamExt;
use futures::future::BoxFuture;
use futures::future::join;
use futures::future::select;
use futures::stream::BoxStream;
use tracing::info;
use crate::worker::Worker;
use crate::worker::context::Context;
use crate::worker::event::Event;

/// 可运行的工作单元 用于表示一个可运行的工作单元，它包含了工作单元的状态、任务流、心跳 Future
/// 和工作单元本身
#[must_use = "A Runnable must be awaited of no jobs will be consumed"]
pub struct Runnable {
    pub(crate) poller: BoxStream<'static, ()>,
    pub(crate) heartbeat: BoxFuture<'static, ()>,
    pub(crate) worker: Worker<Context>,
    pub(crate) running: bool,
}

impl Runnable {
    /// 获取工作单元的句柄
    pub fn get_handle(&self) -> Worker<Context> {
        self.worker.clone()
    }
}

impl fmt::Debug for Runnable {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Runnable")
            .field("poller", &"<stream>")
            .field("heartbeat", &"<future>")
            .field("worker", &self.worker)
            .field("running", &self.running)
            .finish()
    }
}

///实现Future trait
impl Future for Runnable {
    type Output = ();

    ///真正运行，拉取任务
    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {
        let this = self.get_mut();
        let poller = &mut this.poller;
        let heartbeat = &mut this.heartbeat;
        let worker = &mut this.worker;

        //只要任务流没有结束，就一直拉取任务
        let poller_future = async { while poller.next().await.is_some() {} };

        if !this.running {
            worker.start();
            this.running = true;
        }
        //(任务轮询 + 心跳) 持续运行
        let combined = Box::pin(join(poller_future, heartbeat.as_mut()));

        let mut combined = select(
            //拉取任务流
            combined,
            //Context实现Future trait，当Context结束时，结束信号也会结束
            worker.state.clone().map(|_| worker.emit(Event::Stop)),
        )
        .boxed();
        match Pin::new(&mut combined).poll(cx) {
            Poll::Ready(_) => {
                worker.emit(Event::Exit);
                Poll::Ready(())
            }
            Poll::Pending => Poll::Pending,
        }
    }
}
