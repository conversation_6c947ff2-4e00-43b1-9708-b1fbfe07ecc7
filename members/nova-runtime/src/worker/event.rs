use std::fmt;
use std::sync::{Arc, RwLock};
use crate::BoxDynError;
use crate::task::TaskId;
use crate::worker::Worker;

///任务执行器事件
#[derive(Debug)]
pub enum Event {
    ///开始
    Start,
    /// 添加任务
    Engage(TaskId),

    /// 任务执行器空闲
    Idle,

    /// 自定义事件
    Custom(String),

    /// 任务执行器错误
    Error(BoxDynError),
    /// 任务执行器停止
    Stop,
    /// 任务执行器退出
    Exit,
}


impl fmt::Display for Worker<Event> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let event_description = match &self.state {
            Event::Start => "Worker started".to_string(),
            Event::Engage(task_id) => format!("Worker engaged with Task ID: {}", task_id),
            Event::Idle => "Worker is idle".to_string(),
            Event::Custom(msg) => format!("Custom event: {}", msg),
            Event::Error(err) => format!("Worker encountered an error: {}", err),
            Event::Stop => "Worker stopped".to_string(),
            Event::Exit => "Worker completed all pending tasks and exited".to_string(),
        };

        write!(f, "Worker [{}]: {}", self.name(), event_description)
    }
}

/// 事件处理函数
pub type EventHandler = Arc<RwLock<Option<Box<dyn Fn(Worker<Event>) + Send + Sync>>>>;
