use crate::coordinator::shutdown::Shutdown;
use std::fmt;
use std::pin::Pin;
use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};
use std::sync::{Arc, Mutex};
use std::task::{Poll, Waker};
use crate::error::NovaRuntimeError;
use crate::service_fn::FromRequest;
use crate::task::TaskRequest;
use crate::worker::event::{Event, EventHandler};
use crate::worker::trace::Tracked;
use crate::worker::{Worker, WorkerConfig};


/// 任务执行器上下文
#[derive(Clone, Default)]
pub struct Context {
    /// 任务计数器
    pub(crate) task_count: Arc<AtomicUsize>,
    /// 任务唤醒器
    pub(crate) waker: Arc<Mutex<Option<Waker>>>,
    /// 任务执行器是否正在运行
    pub(crate) running: Arc<AtomicBool>,
    /// 任务执行器关闭句柄
    pub(crate) shutdown: Option<Shutdown>,
    /// 事件处理函数
    pub(crate) event_handler: EventHandler,
    /// 任务执行器是否已经准备好接收新任务
    pub(crate) is_ready: Arc<AtomicBool>,
    /// 任务执行器服务名称
    pub(crate) service: String,
}


impl fmt::Debug for Context {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("WorkerContext")
            .field("shutdown", &["Shutdown handle"])
            .field("task_count", &self.task_count)
            .field("running", &self.running)
            .field("service", &self.service)
            .finish()
    }
}


impl Context {

    /// 跟踪任务
    pub fn track<F: Future>(&self, task: F) -> Tracked<F> {
        self.add_task_count();
        //返回跟踪任务
        Tracked {
            ctx: self.clone(),
            task,
        }
    }


    /// 停止任务执行器
    pub fn stop(&self) {
        self.running.store(false, Ordering::Relaxed);
        self.wake()
    }

    ///添加正在运行任务计数
    pub(crate) fn add_task_count(&self) {
        self.task_count.fetch_add(1, Ordering::Relaxed);
    }

    ///减少正在运行任务计数
    pub(crate) fn end_task(&self) {
        if self.task_count.fetch_sub(1, Ordering::Relaxed) == 1 {
            self.wake();
        }
    }

    /// 唤醒任务执行器
    pub(crate) fn wake(&self) {
        if let Ok(waker) = self.waker.lock() {
            if let Some(waker) = &*waker {
                waker.wake_by_ref();
            }
        }
    }


    /// 是否正在运行
    pub fn is_running(&self) -> bool {
        self.running.load(Ordering::Relaxed)
    }

    /// 获取正在运行的任务计数
    pub fn task_count(&self) -> usize {
        self.task_count.load(Ordering::Relaxed)
    }

    /// 是否有正在运行的任务
    pub fn has_pending_tasks(&self) -> bool {
        self.task_count.load(Ordering::Relaxed) > 0
    }

    /// 是否正在关闭
    pub fn is_shutting_down(&self) -> bool {
        self.shutdown
            .as_ref()
            .map(|s| !self.is_running() || s.is_shutting_down())
            .unwrap_or(!self.is_running())
    }

    ///添加唤醒器，注册当前的 Waker，以便在状态改变时被唤醒
    fn add_waker(&self, cx: &mut std::task::Context<'_>) {
        if let Ok(mut waker_guard) = self.waker.lock() {
            if waker_guard
                .as_ref()
                .map_or(true, |stored_waker| !stored_waker.will_wake(cx.waker()))
            {
                *waker_guard = Some(cx.waker().clone());
            }
        }
    }


    /// 检查是否有最近的唤醒器， 检查避免重复
    fn has_recent_waker(&self, cx: &std::task::Context<'_>) -> bool {
        if let Ok(waker_guard) = self.waker.lock() {
            if let Some(stored_waker) = &*waker_guard {
                //确保只有在真正需要时才更新
                return stored_waker.will_wake(cx.waker());
            }
        }
        false
    }


    /// 是否准备好接收新任务
    pub fn is_ready(&self) -> bool {
        self.is_ready.load(Ordering::Acquire) && !self.is_shutting_down()
    }

    /// 获取任务执行器服务名称
    pub fn get_service(&self) -> &String {
        &self.service
    }
}


///实现Future trait，用于等待任务执行器关闭
impl Future for Context {
    type Output = ();

    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<()> {
        let task_count = self.task_count.load(Ordering::Relaxed);
        // 工作者正在关闭，且没有正在运行的任务
        if self.is_shutting_down() && task_count == 0 {
            //异步操作完成
            Poll::Ready(())
        } else {
            // 工作者没有最近的唤醒器，添加唤醒器
            if !self.has_recent_waker(cx) {
                self.add_waker(cx);
            }
            Poll::Pending
        }
    }
}



impl Worker<Context> {

    /// 允许工作者发出事件
    pub fn emit(&self, event: Event) -> bool {
        if let Some(handler) = self.state.event_handler.read().unwrap().as_ref() {
            handler(Worker {
                name:self.name().to_string(),
                state: event,
                config: self.config.clone(),
            });
            return true;
        }
        false
    }

    /// 启动任务执行器
    pub fn start(&self) {
        self.state.running.store(true, Ordering::Relaxed);
        self.state.is_ready.store(true, Ordering::Release);
        self.emit(Event::Start);
    }
}

impl<Req, Ctx> FromRequest<TaskRequest<Req, Ctx>> for Worker<Context> {
    fn from_request(req: &TaskRequest<Req, Ctx>) -> Result<Self, NovaRuntimeError> {
        req.parts.data.get_checked().cloned()
    }
}