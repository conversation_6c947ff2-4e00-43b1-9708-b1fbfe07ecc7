use chrono::Utc;
use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};
use crate::domain::job_definition::JobDefinition;
use crate::status::RunStatus;


/// 工作流定义
///
/// 表示工作流的定义，包含名称、描述、版本和定义内容
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "workflow_definition")]
pub struct WorkflowDefinitionDataBase {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub created_at: DateTimeUtc,
    pub updated_at: DateTimeUtc,
    pub name: String,
    pub description: Option<String>,
    pub version: i32,
    pub definition: Json,
}


/// 工作流定义结构体
///
/// 定义了一个完整的工作流，包含步骤、触发器和元数据信息。
/// 工作流是由多个步骤组成的有向无环图，可以通过不同的触发器启动。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowDefinition {
    /// 工作流的唯一标识符
    pub id: String,
    /// 工作流的版本号
    pub version: i32,
    /// 工作流的名称
    pub name: String,
    /// 工作流的描述信息（可选）
    pub description: Option<String>,
    /// 工作流包含的步骤列表
    pub steps: Vec<JobDefinition>,
    /// 工作流创建时间
    pub created_at: DateTimeUtc,
    /// 工作流最后更新时间
    pub updated_at: DateTimeUtc,
}

impl WorkflowDefinition {
    /// 验证工作流定义的有效性
    ///
    /// 检查工作流的各个组件是否符合要求，包括：
    /// - ID 和名称不能为空
    /// - 必须至少包含一个步骤
    /// - 步骤 ID 必须唯一
    /// - 所有步骤都必须有效
    ///
    /// # 返回值
    ///
    /// * `Ok(())` - 验证通过
    /// * `Err(String)` - 验证失败，返回错误信息
    pub fn validate(&self) -> Result<(), String> {
        if self.id.is_empty() {
            return Err("工作流 ID 不能为空".to_string());
        }
        if self.name.is_empty() {
            return Err("工作流名称不能为空".to_string());
        }

        if self.steps.is_empty() {
            return Err("工作流必须至少包含一个步骤".to_string());
        }

        let step_ids: Vec<&String> = self.steps.iter().map(|s| &s.id).collect();
        let unique_ids: Vec<&String> = step_ids.iter().map(|&&ref id| id).collect();
        if step_ids.len() != unique_ids.len() {
            return Err("步骤 ID 必须唯一".to_string());
        }

        for step in &self.steps {
            step.validate()?;
        }

        Ok(())
    }

    /// 根据 ID 获取步骤
    ///
    /// # 参数
    ///
    /// * `step_id` - 步骤的 ID
    ///
    /// # 返回值
    ///
    /// 返回对应的步骤定义，如果不存在则返回 None
    pub fn get_step(&self, step_id: &str) -> Option<&JobDefinition> {
        self.steps.iter().find(|s| s.id == step_id)
    }

}


/// 工作流运行状态
///
/// 表示工作流的一次具体执行实例，包含运行状态、载荷数据和时间信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowRun {
    /// 运行实例的唯一标识符
    pub id: Uuid,
    /// 关联的工作流 ID
    pub workflow_id: String,
    /// 当前运行状态
    pub status: RunStatus,
    /// 触发工作流时的载荷数据
    pub payload: serde_json::Value,
    /// 开始执行时间
    pub started_at: DateTimeUtc,
    /// 完成执行时间（可选）
    pub completed_at: Option<DateTimeUtc>,
    /// 错误信息（如果执行失败）
    pub error: Option<String>,
}


impl WorkflowRun {
    /// 验证工作流运行的有效性
    ///
    /// 检查运行状态和时间戳的一致性
    ///
    /// # 返回值
    ///
    /// * `Ok(())` - 验证通过
    /// * `Err(String)` - 验证失败，返回错误信息
    pub fn validate(&self) -> Result<(), String> {
        if self.workflow_id.is_empty() {
            return Err("工作流 ID 不能为空".to_string());
        }

        if matches!(self.status, RunStatus::Completed | RunStatus::Failed) && self.completed_at.is_none() {
            return Err("已完成的运行必须有完成时间戳".to_string());
        }

        if matches!(self.status, RunStatus::Failed) && self.error.is_none() {
            return Err("失败的运行必须有错误信息".to_string());
        }

        Ok(())
    }

    /// 检查运行是否处于活跃状态（等待中或运行中）
    ///
    /// # 返回值
    ///
    /// * `true` - 如果运行处于等待或运行状态
    /// * `false` - 如果运行已完成或失败
    pub fn is_active(&self) -> bool {
        matches!(self.status, RunStatus::Pending | RunStatus::Running)
    }

    /// 检查运行是否已完成（成功或失败）
    ///
    /// # 返回值
    ///
    /// * `true` - 如果运行已完成或失败
    /// * `false` - 如果运行仍在进行中
    pub fn is_completed(&self) -> bool {
        matches!(self.status, RunStatus::Completed | RunStatus::Failed)
    }

    /// 获取运行持续时间（毫秒）
    ///
    /// # 返回值
    ///
    /// 返回运行的持续时间，如果尚未完成则返回 None
    pub fn get_duration_ms(&self) -> Option<u64> {
        self.completed_at.map(|completed| {
            (completed - self.started_at).num_milliseconds() as u64
        })
    }
}