use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use crate::status::StepStatus;

pub mod workflow_definition;
pub mod job_definition;


/// 步骤执行结果
///
/// 表示单个步骤的执行结果，包含状态、输出、错误信息和时间信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobResult {
    /// 步骤 ID
    pub step_id: String,
    /// 执行状态
    pub status: StepStatus,
    /// 步骤输出数据（可选）
    pub output: Option<serde_json::Value>,
    /// 错误信息（如果执行失败）
    pub error: Option<String>,
    /// 开始执行时间
    pub started_at: DateTime<Utc>,
    /// 完成执行时间（可选）
    pub completed_at: Option<DateTime<Utc>>,
    /// 执行持续时间（毫秒）
    pub duration_ms: Option<u64>,
}

impl JobResult {
    /// 验证步骤结果的有效性
    ///
    /// 检查步骤结果的状态和时间戳的一致性
    ///
    /// # 返回值
    ///
    /// * `Ok(())` - 验证通过
    /// * `Err(String)` - 验证失败，返回错误信息
    pub fn validate(&self) -> Result<(), String> {
        if self.step_id.is_empty() {
            return Err("步骤 ID 不能为空".to_string());
        }

        if matches!(self.status, StepStatus::Completed | StepStatus::Failed) && self.completed_at.is_none() {
            return Err("已完成的步骤必须有完成时间戳".to_string());
        }

        if matches!(self.status, StepStatus::Failed) && self.error.is_none() {
            return Err("失败的步骤必须有错误信息".to_string());
        }

        Ok(())
    }

    /// 检查步骤是否处于活跃状态（等待中或运行中）
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤处于等待或运行状态
    /// * `false` - 如果步骤已完成或失败
    pub fn is_active(&self) -> bool {
        matches!(self.status, StepStatus::Pending | StepStatus::Running)
    }

    /// 检查步骤是否已完成（成功或失败）
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤已完成或失败
    /// * `false` - 如果步骤仍在进行中
    pub fn is_completed(&self) -> bool {
        matches!(self.status, StepStatus::Completed | StepStatus::Failed)
    }

    /// 获取步骤执行持续时间（毫秒）
    ///
    /// 优先返回预设的持续时间，如果没有则根据时间戳计算
    ///
    /// # 返回值
    ///
    /// 返回步骤的执行持续时间，如果尚未完成则返回 None
    pub fn get_duration_ms(&self) -> Option<u64> {
        self.duration_ms.or_else(|| {
            self.completed_at.map(|completed| {
                (completed - self.started_at).num_milliseconds() as u64
            })
        })
    }
}



/// 条件评估结果
///
/// 表示条件表达式评估的结果，包含是否满足条件、错误信息和元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConditionResult {
    /// 条件是否满足
    pub met: bool,
    /// 条件评估失败时的错误信息
    pub error: Option<String>,
    /// 关于评估的额外元数据
    pub metadata: serde_json::Value,
}

impl ConditionResult {
    /// 创建成功的条件结果
    ///
    /// # 参数
    ///
    /// * `met` - 条件是否满足
    ///
    /// # 返回值
    ///
    /// 返回表示成功评估的条件结果
    pub fn success(met: bool) -> Self {
        Self {
            met,
            error: None,
            metadata: serde_json::json!({}),
        }
    }

    /// 创建失败的条件结果
    ///
    /// # 参数
    ///
    /// * `error` - 错误信息
    ///
    /// # 返回值
    ///
    /// 返回表示评估失败的条件结果
    pub fn failure(error: String) -> Self {
        Self {
            met: false,
            error: Some(error),
            metadata: serde_json::json!({}),
        }
    }
}