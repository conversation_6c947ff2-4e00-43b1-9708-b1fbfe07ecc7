use chrono::Utc;
use sea_orm::prelude::DateTimeUtc;
use serde::{Deserialize, Serialize};
use crate::config::retry::RetryConfig;
use crate::JobResult;
use crate::status::{ParallelGroupStatus, StepStatus};

/// 步骤定义结构体
///
/// 定义了工作流中的单个步骤，包含执行逻辑、依赖关系、控制流和并行执行等配置。
/// 步骤是工作流的基本执行单元，可以是普通的操作步骤，也可以是控制流步骤。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobDefinition {
    /// 步骤的唯一标识符
    pub id: String,
    /// 步骤的名称
    pub name: String,
    /// 步骤的人类可读标题（可选）
    pub title: Option<String>,
    /// 步骤功能的描述信息（可选）
    pub description: Option<String>,
    /// 步骤要执行的操作
    pub action: String,
    /// 步骤执行超时时间（毫秒）
    pub timeout: Option<u64>,
    /// 重试配置
    pub retry: Option<RetryConfig>,

    //----------------------
    // 运行相关参数
    //----------------------
    /// 该步骤依赖的其他步骤 ID 列表
    pub depends_on: Vec<String>,


    //----------------------
    // condition运行相关参数
    //----------------------
    /// 控制流条件类型 (if/elseif/else/endif)
    pub condition_type: Option<ConditionType>,
    /// 用于评估的序列化条件表达式
    pub condition_expression: Option<String>,
    /// 控制流块标识符
    pub control_flow_block: Option<String>,
    /// 该步骤是否属于控制流结构
    pub is_control_flow: bool,

    //----------------------
    // 并行运行相关参数
    //----------------------
    /// 该步骤是否应该并行执行
    pub parallel: Option<bool>,
    /// 并行组标识符，用于分组并行步骤
    pub parallel_group_id: Option<String>,
    /// 并行组中的步骤数量
    pub parallel_step_count: Option<usize>,
    /// 并行组中的步骤是否为竞态条件，true的话任意返回直接返回
    pub race: Option<bool>,

    //----------------------
    // 循环运行相关参数
    //----------------------
    /// 是否为 forEach 循环步骤
    pub for_each: Option<bool>,

    /// 该步骤是否应该暂停工作流执行
    pub pause: Option<bool>,
}


impl JobDefinition {
    /// 验证步骤定义的有效性
    ///
    /// 检查步骤的各个配置是否符合要求，包括：
    /// - ID、名称和操作不能为空
    /// - 重试配置有效
    /// - 控制流配置正确
    /// - 并行执行配置合理
    ///
    /// # 返回值
    ///
    /// * `Ok(())` - 验证通过
    /// * `Err(String)` - 验证失败，返回错误信息
    pub fn validate(&self) -> Result<(), String> {
        if self.id.is_empty() {
            return Err("步骤 ID 不能为空".to_string());
        }

        if self.name.is_empty() {
            return Err("步骤名称不能为空".to_string());
        }

        if self.action.is_empty() {
            return Err("步骤操作不能为空".to_string());
        }

        if let Some(retry) = &self.retry {
            retry.validate()?;
        }

        self.validate_control_flow()?;

        self.validate_parallel_execution()?;

        Ok(())
    }

    /// 验证控制流配置
    ///
    /// 检查控制流步骤的配置是否正确，包括条件类型和表达式的匹配
    fn validate_control_flow(&self) -> Result<(), String> {
        // 如果这是一个控制流步骤，确保它有正确的配置
        if self.is_control_flow {
            if let Some(condition_type) = &self.condition_type {
                match condition_type {
                    ConditionType::If | ConditionType::ElseIf => {
                        if self.condition_expression.is_none() {
                            return Err(format!("{} 步骤必须有条件表达式", condition_type.as_str()));
                        }
                        if self.control_flow_block.is_none() {
                            return Err(format!("{} 步骤必须有控制流块标识符", condition_type.as_str()));
                        }
                    },
                    ConditionType::Else | ConditionType::EndIf => {
                        if self.control_flow_block.is_none() {
                            return Err(format!("{} 步骤必须有控制流块标识符", condition_type.as_str()));
                        }
                    }
                }
            } else {
                return Err("控制流步骤必须有条件类型".to_string());
            }
        }

        Ok(())
    }

    /// 验证并行执行配置
    ///
    /// 检查并行执行步骤的配置是否完整和合理
    fn validate_parallel_execution(&self) -> Result<(), String> {
        if self.parallel.is_some() {
            if self.parallel_group_id.is_none() {
                return Err("并行步骤必须有并行组 ID".to_string());
            }
            if self.parallel_step_count.is_none() {
                return Err("并行步骤必须有并行步骤计数".to_string());
            }
            if self.parallel_step_count.unwrap() == 0 {
                return Err("并行步骤计数必须大于 0".to_string());
            }
        }
        Ok(())
    }

    /// 检查步骤是否有依赖
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤有依赖的其他步骤
    /// * `false` - 如果步骤没有依赖
    pub fn has_dependencies(&self) -> bool {
        !self.depends_on.is_empty()
    }

    /// 获取超时时间（毫秒）
    ///
    /// # 返回值
    ///
    /// 返回步骤的超时时间，如果未设置则返回 None
    pub fn get_timeout_ms(&self) -> Option<u64> {
        self.timeout
    }

    /// 检查步骤是否可以重试
    ///
    /// 注意：这是一个占位符实现，将来会结合实际重试次数进行检查
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤配置了重试策略
    /// * `false` - 如果步骤没有配置重试策略
    pub fn can_retry(&self) -> bool {
        // 将来这里会检查实际的重试次数
        self.retry.is_some()
    }

    /// 检查是否为控制流步骤
    ///
    /// # 返回值
    ///
    /// * `true` - 如果是控制流步骤且有条件类型
    /// * `false` - 如果不是控制流步骤
    pub fn is_control_flow_step(&self) -> bool {
        self.is_control_flow && self.condition_type.is_some()
    }

    /// 检查步骤是否需要条件评估
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤需要进行条件评估（if/elseif）
    /// * `false` - 如果步骤不需要条件评估
    pub fn requires_condition_evaluation(&self) -> bool {
        if let Some(condition_type) = &self.condition_type {
            condition_type.is_conditional()
        } else {
            false
        }
    }

    /// 检查是否为控制流边界步骤
    ///
    /// # 返回值
    ///
    /// * `true` - 如果是控制流边界（if/endif）
    /// * `false` - 如果不是控制流边界
    pub fn is_control_flow_boundary(&self) -> bool {
        if let Some(condition_type) = &self.condition_type {
            condition_type.is_boundary()
        } else {
            false
        }
    }

    /// 获取控制流块 ID
    ///
    /// # 返回值
    ///
    /// 返回控制流块的 ID 引用，如果不存在则返回 None
    pub fn get_control_flow_block_id(&self) -> Option<&String> {
        self.control_flow_block.as_ref()
    }

    /// 获取用于评估的条件表达式
    ///
    /// # 返回值
    ///
    /// 返回条件表达式的引用，如果不存在则返回 None
    pub fn get_condition_expression(&self) -> Option<&String> {
        self.condition_expression.as_ref()
    }

    /// 检查步骤是否应该并行执行
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤配置为并行执行
    /// * `false` - 如果步骤为串行执行
    pub fn is_parallel(&self) -> bool {
        self.parallel.unwrap_or(false)
    }

    /// 检查步骤是否为竞态条件的一部分
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤是竞态条件步骤
    /// * `false` - 如果步骤不是竞态条件步骤
    pub fn is_race(&self) -> bool {
        self.race.unwrap_or(false)
    }

    /// 检查步骤是否为 forEach 循环
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤是 forEach 循环步骤
    /// * `false` - 如果步骤不是 forEach 循环步骤
    pub fn is_for_each(&self) -> bool {
        self.for_each.unwrap_or(false)
    }

    /// 获取并行组 ID（如果步骤是并行的）
    ///
    /// # 返回值
    ///
    /// 返回并行组 ID 的引用，如果不存在则返回 None
    pub fn get_parallel_group_id(&self) -> Option<&String> {
        self.parallel_group_id.as_ref()
    }

    /// 获取并行组中的步骤数量
    ///
    /// # 返回值
    ///
    /// 返回并行组中的步骤数量，如果未设置则返回 None
    pub fn get_parallel_step_count(&self) -> Option<usize> {
        self.parallel_step_count
    }

    /// 检查步骤是否为并行执行步骤（并行、竞态或 forEach）
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤是任何形式的并行执行
    /// * `false` - 如果步骤是串行执行
    pub fn is_parallel_execution(&self) -> bool {
        self.is_parallel() || self.is_race() || self.is_for_each()
    }

    /// 检查步骤是否应该暂停工作流执行
    ///
    /// # 返回值
    ///
    /// * `true` - 如果步骤会暂停工作流
    /// * `false` - 如果步骤不会暂停工作流
    pub fn is_pause_step(&self) -> bool {
        self.pause.unwrap_or(false)
    }
}

/// 控制流条件类型
///
/// 定义了工作流中支持的条件控制结构类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConditionType {
    /// if 条件开始
    If,
    /// else if 条件分支
    ElseIf,
    /// else 默认分支
    Else,
    /// endif 条件结束
    EndIf,
}


impl ConditionType {
    /// 检查是否为需要条件评估的步骤
    ///
    /// # 返回值
    ///
    /// * `true` - 如果是 If 或 ElseIf 类型，需要进行条件评估
    /// * `false` - 如果是 Else 或 EndIf 类型，不需要条件评估
    pub fn is_conditional(&self) -> bool {
        matches!(self, ConditionType::If | ConditionType::ElseIf)
    }

    /// 检查是否为控制流边界步骤
    ///
    /// # 返回值
    ///
    /// * `true` - 如果是 If 或 EndIf 类型，标记控制流的开始或结束
    /// * `false` - 如果是 ElseIf 或 Else 类型，属于控制流内部
    pub fn is_boundary(&self) -> bool {
        matches!(self, ConditionType::If | ConditionType::EndIf)
    }

    /// 获取条件类型的字符串表示
    ///
    /// # 返回值
    ///
    /// 返回对应的字符串标识符
    pub fn as_str(&self) -> &'static str {
        match self {
            ConditionType::If => "if",
            ConditionType::ElseIf => "elseif",
            ConditionType::Else => "else",
            ConditionType::EndIf => "endif",
        }
    }
}

/// 控制流块，用于管理条件执行
///
/// 该结构体表示工作流中的一个控制流块，包含条件判断、执行状态等信息。
/// 支持嵌套的控制流结构，如 if-else 嵌套。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ControlFlowBlock {
    /// 控制流块的唯一标识符
    pub block_id: String,
    /// 条件类型 (if/elseif/else/endif)
    pub condition_type: ConditionType,
    /// 条件是否满足 (用于 if/elseif)
    pub condition_met: bool,
    /// 该块是否已被执行
    pub executed: bool,
    /// 该块的起始步骤 ID
    pub start_step: String,
    /// 该块的结束步骤 ID (如果已知)
    pub end_step: Option<String>,
    /// 嵌套的控制流块
    pub nested_blocks: Vec<ControlFlowBlock>,
    /// 父块 ID (用于嵌套条件)
    pub parent_block_id: Option<String>,
}


impl ControlFlowBlock {
    /// 创建新的控制流块
    ///
    /// # 参数
    ///
    /// * `block_id` - 块的唯一标识符
    /// * `condition_type` - 条件类型
    /// * `start_step` - 起始步骤 ID
    ///
    /// # 返回值
    ///
    /// 返回新创建的控制流块实例
    pub fn new(block_id: String, condition_type: ConditionType, start_step: String) -> Self {
        Self {
            block_id,
            condition_type,
            condition_met: false,
            executed: false,
            start_step,
            end_step: None,
            nested_blocks: Vec::new(),
            parent_block_id: None,
        }
    }

    /// 标记条件为已满足
    pub fn mark_condition_met(&mut self) {
        self.condition_met = true;
    }

    /// 标记块为已执行
    pub fn mark_executed(&mut self) {
        self.executed = true;
    }

    /// 检查该块是否应该被执行
    ///
    /// # 返回值
    ///
    /// * `true` - 如果该块应该被执行
    /// * `false` - 如果该块应该被跳过
    pub fn should_execute(&self) -> bool {
        match self.condition_type {
            ConditionType::If => self.condition_met,
            ConditionType::ElseIf => self.condition_met && !self.executed,
            ConditionType::Else => !self.executed, // Else 在没有前置条件满足时执行
            ConditionType::EndIf => true, // EndIf 总是执行
        }
    }

    /// 添加嵌套块
    ///
    /// # 参数
    ///
    /// * `block` - 要添加的嵌套控制流块
    pub fn add_nested_block(&mut self, block: ControlFlowBlock) {
        self.nested_blocks.push(block);
    }
}


/// 并行步骤组，用于管理并发执行
///
/// 该结构体管理一组需要并行执行的步骤，跟踪它们的执行状态和结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParallelStepGroup {
    /// 并行组的唯一标识符
    pub group_id: String,
    /// 属于此并行组的步骤 ID 列表
    pub step_ids: Vec<String>,
    /// 并行组的当前状态
    pub status: ParallelGroupStatus,
    /// 各个并行步骤的执行结果
    pub results: std::collections::HashMap<String, JobResult>,
    /// 并行组开始执行的时间
    pub started_at: DateTimeUtc,
    /// 并行组完成执行的时间
    pub completed_at: Option<DateTimeUtc>,
    /// 并行组失败时的错误信息
    pub error: Option<String>,
    /// 是否在第一个错误时快速失败
    pub fail_fast: bool,
    /// 整个并行组的最大超时时间
    pub timeout_ms: Option<u64>,
}

impl ParallelStepGroup {
    /// 创建新的并行步骤组
    ///
    /// # 参数
    ///
    /// * `group_id` - 并行组的唯一标识符
    /// * `step_ids` - 属于此组的步骤 ID 列表
    ///
    /// # 返回值
    ///
    /// 返回新创建的并行步骤组实例
    pub fn new(group_id: String, step_ids: Vec<String>) -> Self {
        Self {
            group_id,
            step_ids,
            status: ParallelGroupStatus::Pending,
            results: std::collections::HashMap::new(),
            started_at: Utc::now(),
            completed_at: None,
            error: None,
            fail_fast: true, // 默认快速失败
            timeout_ms: None,
        }
    }

    /// 标记组为运行状态
    pub fn mark_running(&mut self) {
        self.status = ParallelGroupStatus::Running;
        self.started_at = Utc::now();
    }

    /// 标记组为已完成状态
    pub fn mark_completed(&mut self) {
        self.status = ParallelGroupStatus::Completed;
        self.completed_at = Some(Utc::now());
    }

    /// 标记组为失败状态
    ///
    /// # 参数
    ///
    /// * `error` - 失败的错误信息
    pub fn mark_failed(&mut self, error: String) {
        self.status = ParallelGroupStatus::Failed;
        self.completed_at = Some(Utc::now());
        self.error = Some(error);
    }

    /// 标记组为部分失败状态
    ///
    /// # 参数
    ///
    /// * `error` - 部分失败的错误信息
    pub fn mark_partially_failed(&mut self, error: String) {
        self.status = ParallelGroupStatus::PartiallyFailed;
        self.completed_at = Some(Utc::now());
        self.error = Some(error);
    }

    /// 向组中添加步骤结果
    ///
    /// # 参数
    ///
    /// * `step_id` - 步骤 ID
    /// * `result` - 步骤执行结果
    pub fn add_step_result(&mut self, step_id: String, result: JobResult) {
        self.results.insert(step_id, result);
    }

    /// 检查组中所有步骤是否都已完成
    ///
    /// # 返回值
    ///
    /// * `true` - 如果所有步骤都已成功完成
    /// * `false` - 如果还有步骤未完成或失败
    pub fn is_completed(&self) -> bool {
        self.step_ids.iter().all(|step_id| {
            self.results.contains_key(step_id.as_str()) &&
                matches!(self.results[step_id.as_str()].status, StepStatus::Completed)
        })
    }

    /// 检查组中是否有步骤失败
    ///
    /// # 返回值
    ///
    /// * `true` - 如果有任何步骤失败
    /// * `false` - 如果没有步骤失败
    pub fn has_failures(&self) -> bool {
        self.step_ids.iter().any(|step_id| {
            self.results.contains_key(step_id.as_str()) &&
                matches!(self.results[step_id.as_str()].status, StepStatus::Failed)
        })
    }

    /// 获取已完成步骤的数量
    ///
    /// # 返回值
    ///
    /// 返回成功完成的步骤数量
    pub fn completed_count(&self) -> usize {
        self.step_ids.iter().filter(|step_id| {
            self.results.contains_key(step_id.as_str()) &&
                matches!(self.results[step_id.as_str()].status, StepStatus::Completed)
        }).count()
    }

    /// 获取失败步骤的数量
    ///
    /// # 返回值
    ///
    /// 返回执行失败的步骤数量
    pub fn failed_count(&self) -> usize {
        self.step_ids.iter().filter(|step_id| {
            self.results.contains_key(step_id.as_str()) &&
                matches!(self.results[step_id.as_str()].status, StepStatus::Failed)
        }).count()
    }

    /// 获取并行组执行的持续时间
    ///
    /// # 返回值
    ///
    /// 返回并行组的执行持续时间（毫秒），如果尚未完成则返回 None
    pub fn get_duration_ms(&self) -> Option<u64> {
        self.completed_at.map(|completed| {
            (completed - self.started_at).num_milliseconds() as u64
        })
    }
}