use sea_orm::sqlx::types::uuid;
use thiserror::Error;

/// 核心引擎错误类型
///
/// 定义了核心引擎运行过程中可能遇到的所有错误类型，
/// 包括数据库错误、序列化错误、网络错误等。
#[derive(Error, Debug)]
pub enum CoreError {
    /// 数据库操作错误
    #[error("数据库错误: {0}")]
    Database(#[from] sea_orm::DbErr),

    /// JSON 序列化/反序列化错误
    #[error("JSON 序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),

    /// 无效的工作流定义
    #[error("无效的工作流定义: {0}")]
    InvalidWorkflow(String),

    /// 工作流未找到
    #[error("工作流未找到: {0}")]
    WorkflowNotFound(String),

    /// 运行实例未找到
    #[error("运行实例未找到: {0}")]
    RunNotFound(String),

    /// 步骤未找到
    #[error("步骤未找到: {0}")]
    JobNotFound(String),

    /// 步骤执行失败
    #[error("步骤执行失败: {0}")]
    JobExecution(String),

    /// 状态管理错误
    #[error("状态管理错误: {0}")]
    State(String),

    /// 无效的状态转换
    #[error("无效的状态转换: {0}")]
    InvalidState(String),

    /// 配置错误
    #[error("配置错误: {0}")]
    Configuration(String),

    /// 验证错误
    #[error("验证错误: {0}")]
    Validation(String),

    /// 无效的触发器配置
    #[error("无效的触发器配置: {0}")]
    InvalidTrigger(String),

    /// 触发器未找到
    #[error("触发器未找到: {0}")]
    TriggerNotFound(String),

    /// 日期解析错误
    #[error("日期解析错误: {0}")]
    DateParse(#[from] chrono::ParseError),

    /// UUID 解析错误
    #[error("UUID 解析错误: {0}")]
    UuidParse(#[from] uuid::Error),

    /// 内部错误
    #[error("内部错误: {0}")]
    Internal(String),
}

/// 核心操作的结果类型
///
/// 这是一个类型别名，用于简化核心引擎中所有操作的返回类型。
/// 所有可能失败的操作都应该返回这个类型。
pub type CoreResult<T> = Result<T, CoreError>;