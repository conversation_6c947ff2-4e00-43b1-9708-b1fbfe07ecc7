use sea_orm::prelude::DateTimeUtc;
use serde::{Deserialize, Serialize};
use crate::JobResult;
use crate::status::{RunStatus, StepStatus};

/// 工作流完成上下文，用于钩子执行
///
/// 包含工作流完成时的所有相关信息，用于执行完成后的钩子函数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowCompletionContext {
    /// 运行 ID
    pub run_id: String,
    /// 工作流 ID
    pub workflow_id: String,
    /// 最终运行状态
    pub status: RunStatus,
    /// 所有已完成步骤的结果
    pub completed_steps: Vec<JobResult>,
    /// 工作流失败时的错误信息
    pub error: Option<String>,
    /// 总执行持续时间（毫秒）
    pub duration_ms: Option<u64>,
    /// 工作流开始时间
    pub started_at: DateTimeUtc,
    /// 工作流完成时间
    pub completed_at: DateTimeUtc,
    /// 触发工作流的原始载荷
    pub payload: serde_json::Value,
    /// 最终工作流输出（最后一个步骤的结果）
    pub final_output: Option<serde_json::Value>,
}

impl WorkflowCompletionContext {
    /// 创建新的完成上下文
    ///
    /// # 参数
    ///
    /// * `run_id` - 运行 ID
    /// * `workflow_id` - 工作流 ID
    /// * `status` - 最终运行状态
    /// * `completed_steps` - 已完成步骤列表
    /// * `error` - 错误信息（可选）
    /// * `started_at` - 开始时间
    /// * `completed_at` - 完成时间
    /// * `payload` - 原始载荷
    ///
    /// # 返回值
    ///
    /// 返回新创建的工作流完成上下文
    pub fn new(
        run_id: String,
        workflow_id: String,
        status: RunStatus,
        completed_steps: Vec<JobResult>,
        error: Option<String>,
        started_at: DateTimeUtc,
        completed_at: DateTimeUtc,
        payload: serde_json::Value,
    ) -> Self {
        let duration_ms = Some((completed_at - started_at).num_milliseconds() as u64);
        let final_output = completed_steps.last().and_then(|step| step.output.clone());

        Self {
            run_id,
            workflow_id,
            status,
            completed_steps,
            error,
            duration_ms,
            started_at,
            completed_at,
            payload,
            final_output,
        }
    }

    /// 检查工作流是否成功完成
    ///
    /// # 返回值
    ///
    /// * `true` - 如果工作流成功完成
    /// * `false` - 如果工作流未成功完成
    pub fn is_success(&self) -> bool {
        matches!(self.status, RunStatus::Completed)
    }

    /// 检查工作流是否失败
    ///
    /// # 返回值
    ///
    /// * `true` - 如果工作流执行失败
    /// * `false` - 如果工作流未失败
    pub fn is_failure(&self) -> bool {
        matches!(self.status, RunStatus::Failed)
    }

    /// 获取已完成步骤的数量
    ///
    /// # 返回值
    ///
    /// 返回已完成步骤的总数
    pub fn completed_step_count(&self) -> usize {
        self.completed_steps.len()
    }

    /// 获取失败步骤的数量
    ///
    /// # 返回值
    ///
    /// 返回执行失败的步骤数量
    pub fn failed_step_count(&self) -> usize {
        self.completed_steps.iter()
            .filter(|step| matches!(step.status, StepStatus::Failed))
            .count()
    }
}