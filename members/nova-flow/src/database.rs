use std::time::Duration;
use sea_orm::{ConnectOptions, Database};
use crate::config::CoreConfig;

#[derive(Debug, Clone)]
pub struct FlowDb {
    pub conn: sea_orm::DatabaseConnection,
}

impl FlowDb {
    pub async fn init(config: &CoreConfig) -> Self {
        let mut opt = ConnectOptions::new(&config.database.default_path);
        opt.max_connections(config.database.max_connections)
            .min_connections(config.database.min_connections)
            .connect_timeout(Duration::from_millis(config.database.connection_timeout_ms))
            .acquire_timeout(Duration::from_millis(config.database.acquire_timeout_ms))
            .idle_timeout(Duration::from_millis(config.database.idle_timeout_ms))
            .max_lifetime(Duration::from_millis(config.database.max_lifetime_ms))
            .sqlx_logging(config.database.open_log);
        let db = Database::connect(opt).await.unwrap();
        Self { conn: db }
    }
}
