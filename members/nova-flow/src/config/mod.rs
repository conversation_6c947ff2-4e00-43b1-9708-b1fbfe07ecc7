pub mod retry;
mod priority;
pub use priority::JobPriority;

/// 核心配置结构
///
/// 包含所有核心组件的配置信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct CoreConfig {
    /// 数据库配置
    pub database: DatabaseConfig,
}

impl Default for CoreConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig::default(),
        }
    }
}

///
/// 定义数据库连接和操作参数
#[derive(Debug, <PERSON><PERSON>)]
pub struct DatabaseConfig {
    /// 默认数据库路径
    pub default_path: String,
    /// 连接超时时间（毫秒）
    pub connection_timeout_ms: u64,
    /// 连接获取超时时间（毫秒）
    pub acquire_timeout_ms: u64,
    /// 连接空闲超时时间（毫秒）
    pub idle_timeout_ms: u64,
    /// 连接最大生命周期（毫秒）
    pub max_lifetime_ms: u64,
    /// 最大连接数
    pub max_connections: u32,
    /// 最小连接数
    pub min_connections: u32,
    /// 是否开启日志
    pub open_log: bool,

}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            default_path: "mysql://root:123456@localhost:3306/nova".to_string(),
            connection_timeout_ms: 3000,
            acquire_timeout_ms: 3000,
            idle_timeout_ms: 60000,
            max_lifetime_ms: 60000,
            max_connections: 100,
            min_connections: 5,
            open_log: true
        }
    }
}
