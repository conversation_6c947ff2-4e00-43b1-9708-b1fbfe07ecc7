use serde::{Deserialize, Serialize};

/// 重试配置
///
/// 定义了步骤失败时的重试策略，包括最大重试次数和退避时间
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// 最大重试次数
    pub max_attempts: u32,
    /// 重试间隔时间（毫秒）
    pub backoff_ms: u64,
    pub max_backoff_ms: u64
}

impl RetryConfig {
    /// 验证重试配置的有效性
    ///
    /// 检查重试次数和退避时间是否合理
    ///
    /// # 返回值
    ///
    /// * `Ok(())` - 验证通过
    /// * `Err(String)` - 验证失败，返回错误信息
    pub fn validate(&self) -> Result<(), String> {
        if self.max_attempts == 0 {
            return Err("最大重试次数必须大于 0".to_string());
        }

        if self.backoff_ms == 0 {
            return Err("退避时间必须大于 0".to_string());
        }

        Ok(())
    }

    /// 获取总重试时间（毫秒）
    ///
    /// 计算所有重试尝试的总时间
    ///
    /// # 返回值
    ///
    /// 返回总重试时间（毫秒）
    pub fn get_total_retry_time_ms(&self) -> u64 {
        self.max_attempts as u64 * self.backoff_ms
    }
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 0,
            backoff_ms: 1000,
        }
    }
}
