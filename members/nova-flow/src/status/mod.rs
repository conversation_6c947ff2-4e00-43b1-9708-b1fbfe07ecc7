use serde::{Deserialize, Serialize};

/// 运行状态枚举
///
/// 定义了工作流运行的各种状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "PascalCase")]
pub enum RunStatus {
    /// 等待执行
    Pending,
    /// 正在运行
    Running,
    /// 执行完成
    Completed,
    /// 执行失败
    Failed,
    /// 已取消
    Cancelled,
}

impl RunStatus {
    /// 检查状态是否为终止状态（不能再进行状态转换）
    ///
    /// # 返回值
    ///
    /// * `true` - 如果是终止状态（完成、失败或取消）
    /// * `false` - 如果是非终止状态（等待或运行中）
    pub fn is_terminal(&self) -> bool {
        matches!(self, RunStatus::Completed | RunStatus::Failed | RunStatus::Cancelled)
    }

    /// 获取状态的字符串表示
    ///
    /// # 返回值
    ///
    /// 返回状态的字符串标识符
    pub fn as_str(&self) -> &'static str {
        match self {
            RunStatus::Pending => "pending",
            RunStatus::Running => "running",
            RunStatus::Completed => "completed",
            RunStatus::Failed => "failed",
            RunStatus::Cancelled => "cancelled",
        }
    }
}


/// 步骤状态枚举
///
/// 定义了工作流步骤的各种执行状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "PascalCase")]
pub enum StepStatus {
    /// 等待执行
    Pending,
    /// 正在运行
    Running,
    /// 执行完成
    Completed,
    /// 执行失败
    Failed,
    /// 已跳过
    Skipped,
    Retrying,
}


impl StepStatus {
    /// 检查状态是否为终止状态（不能再进行状态转换）
    ///
    /// # 返回值
    ///
    /// * `true` - 如果是终止状态（完成、失败或跳过）
    /// * `false` - 如果是非终止状态（等待或运行中）
    pub fn is_terminal(&self) -> bool {
        matches!(self, StepStatus::Completed | StepStatus::Failed | StepStatus::Skipped)
    }

    /// 获取状态的字符串表示
    ///
    /// # 返回值
    ///
    /// 返回状态的字符串标识符
    pub fn as_str(&self) -> &'static str {
        match self {
            StepStatus::Pending => "pending",
            StepStatus::Running => "running",
            StepStatus::Completed => "completed",
            StepStatus::Failed => "failed",
            StepStatus::Skipped => "skipped",
        }
    }
}



/// 并行执行状态
///
/// 定义了并行步骤组的各种执行状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ParallelGroupStatus {
    /// 组等待执行
    Pending,
    /// 组正在运行
    Running,
    /// 组中所有步骤都成功完成
    Completed,
    /// 组中所有步骤都失败
    Failed,
    /// 组中部分步骤成功，部分失败
    PartiallyFailed,
    /// 组执行超时
    TimedOut,
}

impl ParallelGroupStatus {
    /// 检查是否为终止状态
    ///
    /// # 返回值
    ///
    /// * `true` - 如果是终止状态（完成、失败、部分失败或超时）
    /// * `false` - 如果是非终止状态（等待或运行中）
    pub fn is_terminal(&self) -> bool {
        matches!(self,
            ParallelGroupStatus::Completed |
            ParallelGroupStatus::Failed |
            ParallelGroupStatus::PartiallyFailed |
            ParallelGroupStatus::TimedOut
        )
    }

    /// 获取状态的字符串表示
    ///
    /// # 返回值
    ///
    /// 返回状态的字符串标识符
    pub fn as_str(&self) -> &'static str {
        match self {
            ParallelGroupStatus::Pending => "pending",
            ParallelGroupStatus::Running => "running",
            ParallelGroupStatus::Completed => "completed",
            ParallelGroupStatus::Failed => "failed",
            ParallelGroupStatus::PartiallyFailed => "partially_failed",
            ParallelGroupStatus::TimedOut => "timed_out",
        }
    }
}