use meilisearch_sdk::client::Client;
use serde::{Deserialize, Serialize};
use serde_json::json;

#[derive(Serialize, Deserialize, Debug)]
struct Movie {
    id: usize,
    title: String,
    genres: Vec<String>,
}


#[tokio::main(flavor = "current_thread")]
async fn main() {
    // Create a client (without sending any request so that can't fail)
    let client = Client::new("http://localhost:7700/", Some("a8f6e4f9f7f2c73185c8f897ee15b971241e9f40566130143a096f8a6bb67236")).unwrap();

    // An index is where the documents are stored.
    let movies = client.index("movies");

    // Add some movies in the index. If the index 'movies' does not exist, <PERSON><PERSON><PERSON><PERSON> creates it when you first add the documents.
    movies.add_documents(&[
        Movie { id: 11, title: String::from("CarolDad"), genres: vec!["Romance".to_string(), "Drama".to_string()] },
    ], Some("id")).await.unwrap();

    let res = movies.search().with_query("id=1").execute::<Movie>().await.unwrap();
    println!("{}", json!(res));
}