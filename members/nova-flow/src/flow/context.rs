use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use crate::{CoreError, JobResult};
use crate::workflow_definition::WorkflowRun;

//
/// 包含步骤执行所需的所有必要信息，包括工作流状态、载荷数据、
/// 已完成步骤的结果以及执行元数据。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlowContext {
    /// 此工作流运行的唯一标识符
    pub run_id: String,
    /// 工作流的唯一标识符
    pub workflow_id: String,
    /// 当前正在执行的步骤名称
    pub step_name: String,
    /// 工作流的输入载荷数据
    pub payload: serde_json::Value,
    /// 已完成步骤的结果映射
    pub steps: HashMap<String, JobResult>,
    /// 当前工作流运行状态
    pub run: WorkflowRun,
    /// 执行相关的元数据
    pub metadata: ContextMetadata,
}

/// 上下文执行的元数据
///
/// 包含执行过程中的各种元信息，如时间戳、步骤索引、超时设置等
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextMetadata {
    /// 上下文创建时的时间戳
    pub created_at: String,
    /// 当前步骤索引
    pub step_index: usize,
    /// 步骤总数
    pub total_steps: usize,
    /// 执行超时时间（秒）
    pub timeout: Option<u64>,
    /// 当前步骤的重试次数
    pub retry_count: u32,
    /// 允许的最大重试次数
    pub max_retries: u32,
    /// 上下文版本，用于兼容性检查
    pub version: String,
    /// 数据完整性校验和
    #[serde(skip_serializing_if = "Option::is_none")]
    pub checksum: Option<String>,
}



impl FlowContext {
    /// 从任务数据创建新的上下文
    /// 如果必需的参数为空，返回验证错误
    pub fn new(
        run_id: String,
        workflow_id: String,
        step_name: String,
        payload: serde_json::Value,
        run: WorkflowRun,
        completed_steps: Vec<JobResult>,
    ) -> Result<Self, CoreError> {
        if run_id.is_empty() {
            return Err(CoreError::Validation("运行 ID 不能为空".to_string()));
        }
        if workflow_id.is_empty() {
            return Err(CoreError::Validation("工作流 ID 不能为空".to_string()));
        }
        if step_name.is_empty() {
            return Err(CoreError::Validation("步骤名称不能为空".to_string()));
        }

        let mut steps = HashMap::new();
        for step_result in completed_steps {
            steps.insert(step_result.step_id.clone(), step_result);
        }

        let metadata = ContextMetadata {
            created_at: chrono::Utc::now().to_rfc3339(),
            step_index: 0, // 将由调用者更新
            total_steps: 0, // 将由调用者更新
            timeout: None,
            retry_count: 0,
            max_retries: 3,
            version: "1.0.0".to_string(),
            checksum: None,
        };

        Ok(FlowContext {
            run_id,
            workflow_id,
            step_name,
            payload,
            steps,
            run,
            metadata
        })
    }

    /// 获取已完成步骤的结果
    ///
    /// # 参数
    ///
    /// * `step_name` - 步骤名称
    ///
    /// # 返回值
    ///
    /// 返回步骤结果的引用，如果步骤不存在则返回 None
    pub fn get_step_result(&self, step_name: &str) -> Option<&JobResult> {
        self.steps.get(step_name)
    }

    /// 获取所有已完成步骤的结果
    ///
    /// # 返回值
    ///
    /// 返回所有已完成步骤结果的引用列表
    pub fn get_completed_steps(&self) -> Vec<&JobResult> {
        self.steps.values().collect()
    }

    /// 更新步骤元数据
    ///
    /// # 参数
    ///
    /// * `step_index` - 当前步骤索引
    /// * `total_steps` - 步骤总数
    pub fn update_step_metadata(&mut self, step_index: usize, total_steps: usize) {
        self.metadata.step_index = step_index;
        self.metadata.total_steps = total_steps;
    }

    /// 设置执行超时时间
    ///
    /// # 参数
    ///
    /// * `timeout_seconds` - 超时时间（秒）
    pub fn set_timeout(&mut self, timeout_seconds: u64) {
        self.metadata.timeout = Some(timeout_seconds);
    }

    /// 增加重试次数并检查是否允许重试
    ///
    /// # 返回值
    ///
    /// 如果仍允许重试返回 true，否则返回 false
    pub fn increment_retry(&mut self) -> bool {
        self.metadata.retry_count += 1;
        self.metadata.retry_count <= self.metadata.max_retries
    }

    /// 重置重试次数
    pub fn reset_retry_count(&mut self) {
        self.metadata.retry_count = 0;
    }

    /// 验证上下文数据
    ///
    /// 检查上下文数据的有效性，包括必需字段和载荷大小限制
    ///
    /// # 返回值
    ///
    /// 如果验证通过返回 Ok(())，否则返回验证错误
    pub fn validate(&self) -> Result<(), CoreError> {
        if self.run_id.is_empty() {
            return Err(CoreError::Validation("运行 ID 不能为空".to_string()));
        }
        if self.workflow_id.is_empty() {
            return Err(CoreError::Validation("工作流 ID 不能为空".to_string()));
        }
        if self.step_name.is_empty() {
            return Err(CoreError::Validation("步骤名称不能为空".to_string()));
        }

        let payload_size = serde_json::to_string(&self.payload)
            .map_err(|e| CoreError::Serialization(e))?
            .len();
        if payload_size > 10_000_000 { // 10MB 限制
            return Err(CoreError::Validation(format!(
                "载荷过大: {} 字节 (最大: 10MB)", payload_size
            )));
        }

        Ok(())
    }

    /// 计算上下文复杂度评分（1-10）
    ///
    /// 基于载荷大小、步骤数量和数据嵌套深度计算复杂度评分
    ///
    /// # 返回值
    ///
    /// 返回 1-10 的复杂度评分
    pub fn calculate_complexity_score(&self) -> u8 {
        let mut score = 1u8;

        if let Some(payload_size) = serde_json::to_string(&self.payload).ok().map(|s| s.len()) {
            if payload_size > 100_000 { score += 2; } // 大载荷
            else if payload_size > 10_000 { score += 1; } // 中等载荷
        }

        if self.steps.len() > 50 { score += 2; }
        else if self.steps.len() > 10 { score += 1; }

        if self.has_deep_nesting(&self.payload, 0) { score += 1; }

        score.min(10)
    }

    /// 检查 JSON 值是否有深层嵌套
    ///
    /// 递归检查 JSON 结构的嵌套深度
    ///
    /// # 参数
    ///
    /// * `value` - 要检查的 JSON 值
    /// * `depth` - 当前嵌套深度
    ///
    /// # 返回值
    ///
    /// 如果嵌套深度超过 5 层返回 true，否则返回 false
    fn has_deep_nesting(&self, value: &serde_json::Value, depth: u8) -> bool {
        if depth > 5 { return true; }

        match value {
            serde_json::Value::Object(map) => {
                map.values().any(|v| self.has_deep_nesting(v, depth + 1))
            }
            serde_json::Value::Array(arr) => {
                arr.iter().any(|v| self.has_deep_nesting(v, depth + 1))
            }
            _ => false,
        }
    }

    /// Generate a checksum for the context
    pub fn generate_checksum(&self) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        self.run_id.hash(&mut hasher);
        self.workflow_id.hash(&mut hasher);
        self.step_name.hash(&mut hasher);

        // Hash the payload as a string
        if let Ok(payload_str) = serde_json::to_string(&self.payload) {
            payload_str.hash(&mut hasher);
        }

        format!("{:x}", hasher.finish())
    }

    /// Convert context to JSON string
    pub fn to_json(&self) -> Result<String, CoreError> {
        // Generate checksum before serialization
        let checksum = self.generate_checksum();

        let mut context_for_serialization = self.clone();
        context_for_serialization.metadata.checksum = Some(checksum);
        let complexity_score = self.calculate_complexity_score();
        let start_time = std::time::Instant::now();
        let json_result = serde_json::to_string(&context_for_serialization);
        let serialization_duration = start_time.elapsed();
        let json_string = json_result.map_err(|e| CoreError::Serialization(e))?;
        let size_bytes = json_string.len();


        // Serialize again with the updated info
        serde_json::to_string(&context_for_serialization)
            .map_err(|e| CoreError::Serialization(e))
    }

    /// Convert context to compressed JSON string
    pub fn to_json_compressed(&self) -> Result<String, CoreError> {
        // For now, just return the regular JSON
        // TODO: Implement actual compression when needed
        self.to_json()
    }

    /// Create context from JSON string
    pub fn from_json(json: &str) -> Result<Self, CoreError> {
        let context: FlowContext = serde_json::from_str(json)
            .map_err(|e| CoreError::Serialization(e))?;

        context.validate()?;

        Ok(context)
    }

    /// Convert context to serde_json::Value
    pub fn to_json_value(&self) -> Result<serde_json::Value, CoreError> {
        serde_json::to_value(self)
            .map_err(|e| CoreError::Serialization(e))
    }

    /// Get the size of the context in bytes
    pub fn size_bytes(&self) -> Result<usize, CoreError> {
        let json_string = serde_json::to_string(self)
            .map_err(|e| CoreError::Serialization(e))?;
        Ok(json_string.len())
    }

    /// Check if context is oversized
    pub fn is_oversized(&self, max_size_bytes: usize) -> Result<bool, CoreError> {
        Ok(self.size_bytes()? > max_size_bytes)
    }
}