use std::collections::HashMap;
use chrono::Utc;
use sea_orm::prelude::DateTimeUtc;
use serde::{Deserialize, Serialize};
use tracing::log::info;
use uuid::Uuid;
use crate::config::JobPriority;
use crate::config::retry::RetryConfig;
use crate::{CoreError, JobResult};
use crate::job_definition::JobDefinition;
use crate::status::StepStatus;
use crate::workflow_definition::{WorkflowDefinition, WorkflowRun};

/// 工作流执行的主要任务结构
///
/// 包含任务执行所需的所有信息，包括状态、载荷、依赖关系等
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Job {
    /// 任务唯一标识符
    pub id: String,
    /// 所属工作流 ID
    pub workflow_id: String,
    /// 所属运行实例 ID
    pub run_id: String,
    /// 步骤名称
    pub step_name: String,
    /// 任务当前状态
    pub state: StepStatus,
    /// 任务优先级
    pub priority: JobPriority,
    /// 任务载荷数据
    pub payload: serde_json::Value,
    /// 任务执行结果
    pub result: Option<JobResult>,
    /// 重试配置
    pub retry_config: RetryConfig,
    /// 任务元数据
    pub metadata: JobMetadata,
    /// 依赖的任务 ID 列表
    pub dependencies: Vec<String>,
    /// 超时时间（毫秒）
    pub timeout_ms: Option<u64>,
    /// 额外的上下文数据
    pub context: HashMap<String, serde_json::Value>,
}




/// 用于跟踪和调试的任务元数据
///
/// 记录任务的生命周期信息和执行状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobMetadata {
    /// 任务创建时间
    pub created_at: DateTimeUtc,
    /// 任务最后更新时间
    pub updated_at: DateTimeUtc,
    /// 任务开始执行时间
    pub started_at: Option<DateTimeUtc>,
    /// 任务完成时间
    pub completed_at: Option<DateTimeUtc>,
    /// 尝试执行次数
    pub attempt_count: u32,
    /// 最后一次错误信息
    pub last_error: Option<String>,
    /// 自定义标签
    pub tags: HashMap<String, String>,
}

impl Default for JobMetadata {
    fn default() -> Self {
        Self {
            created_at: Utc::now(),
            updated_at: Utc::now(),
            started_at: None,
            completed_at: None,
            attempt_count: 0,
            last_error: None,
            tags: HashMap::new(),
        }
    }
}


impl Job {
    /// 创建新任务
    /// 返回初始化完成的任务实例
    pub fn new(
        workflow_id: String,
        run_id: String,
        step_name: String,
        payload: serde_json::Value,
        priority: JobPriority,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            workflow_id,
            run_id,
            step_name,
            state: StepStatus::Pending,
            priority,
            payload,
            result: None,
            retry_config: RetryConfig::default(),
            metadata: JobMetadata::default(),
            dependencies: Vec::new(),
            timeout_ms: None,
            context: HashMap::new(),
        }
    }

    /// 从工作流步骤定义创建任务
    pub fn from_workflow_step(
        workflow: &WorkflowDefinition,
        run: &WorkflowRun,
        step_name: &str,
        payload: serde_json::Value,
    ) -> Result<Self, CoreError> {
        let step = workflow
            .steps
            .iter()
            .find(|s| s.id == step_name)
            .ok_or_else(|| {
                CoreError::InvalidWorkflow(format!("步骤 '{}' 在工作流中未找到", step_name))
            })?;

        step.validate()
            .map_err(|e| CoreError::InvalidWorkflow(e))?;

        let mut job = Self::new(
            workflow.id.clone(),
            run.id.to_string(),
            step_name.to_string(),
            payload,
            Self::determine_priority(step, workflow),
        );
        // 应用步骤特定配置
        Self::apply_step_configuration(&mut job, step, workflow)?;
        //设置依赖，没有显示依赖就是上一个
        Self::setup_dependencies(&mut job, step, workflow)?;
        Self::add_workflow_context(&mut job, workflow, run)?;
        job.validate()?;
        Ok(job)
    }

    /// 为工作流运行中的所有步骤创建任务
    /// 返回为所有步骤创建的任务列表
    pub fn create_workflow_jobs(
        workflow: &WorkflowDefinition,
        run: &WorkflowRun,
        payload: serde_json::Value,
    ) -> Result<Vec<Self>, CoreError> {
        info!("为工作流创建任务: {} 运行: {}", workflow.id, run.id);
        let mut jobs = Vec::new();
        for step in &workflow.steps {
            let job = Self::from_workflow_step(workflow, run, &step.id, payload.clone())?;
            jobs.push(job);
        }
        info!("为工作流创建了 {} 个任务: {}", jobs.len(), workflow.id);
        Ok(jobs)
    }

    /// 为工作流运行中的指定步骤子集创建任务
    /// 返回为指定步骤创建的任务列表
    pub fn create_step_jobs(
        workflow: &WorkflowDefinition,
        run: &WorkflowRun,
        step_ids: &[String],
        payload: serde_json::Value,
    ) -> Result<Vec<Self>, CoreError> {
        info!("为步骤创建任务: {:?} 工作流: {} 运行: {}", step_ids, workflow.id, run.id);

        let mut jobs = Vec::new();
        for step_id in step_ids {
            if !workflow.steps.iter().any(|s| s.id == *step_id) {
                return Err(CoreError::InvalidWorkflow(format!(
                    "步骤 '{}' 在工作流 '{}' 中未找到",
                    step_id, workflow.id
                )));
            }
        }
        for step_id in step_ids {
            let job = Self::from_workflow_step(workflow, run, step_id, payload.clone())?;
            jobs.push(job);
        }
        info!("为指定步骤创建了 {} 个任务，工作流: {}", jobs.len(), workflow.id);
        Ok(jobs)
    }

    /// 获取工作流运行中特定步骤的任务 ID
    ///
    /// # 参数
    ///
    /// * `workflow_id` - 工作流 ID
    /// * `run_id` - 运行实例 ID
    /// * `step_id` - 步骤 ID
    ///
    /// # 返回值
    ///
    /// 返回格式化的任务 ID
    pub fn get_job_id(workflow_id: &str, run_id: &str, step_id: &str) -> String {
        format!("{}:{}:{}", workflow_id, run_id, step_id)
    }

    /// 解析任务 ID 以提取工作流、运行和步骤信息
    ///
    /// # 参数
    ///
    /// * `job_id` - 要解析的任务 ID
    ///
    /// # 返回值
    ///
    /// 返回包含工作流 ID、运行 ID 和步骤 ID 的元组
    ///
    /// # 错误
    ///
    /// 如果任务 ID 格式无效，返回验证错误
    pub fn parse_job_id(job_id: &str) -> Result<(String, String, String), CoreError> {
        let parts: Vec<&str> = job_id.split(':').collect();
        if parts.len() != 3 {
            return Err(CoreError::Validation(format!(
                "无效的任务 ID 格式: {}. 期望格式: workflow_id:run_id:step_id",
                job_id
            )));
        }

        Ok((parts[0].to_string(), parts[1].to_string(), parts[2].to_string()))
    }

    /// 检查此任务是否依赖于另一个任务
    ///
    /// # 参数
    ///
    /// * `other_job_id` - 其他任务的 ID
    ///
    /// # 返回值
    ///
    /// 如果存在依赖关系返回 true，否则返回 false
    pub fn depends_on_job(&self, other_job_id: &str) -> bool {
        self.dependencies.contains(&other_job_id.to_string())
    }

    /// 获取此任务依赖的所有任务
    ///
    /// # 返回值
    ///
    /// 返回依赖任务 ID 的切片
    pub fn get_dependency_jobs(&self) -> &[String] {
        &self.dependencies
    }

    /// 为此任务添加依赖
    ///
    /// # 参数
    ///
    /// * `dependency_job_id` - 依赖任务的 ID
    pub fn add_dependency(&mut self, dependency_job_id: String) {
        if !self.dependencies.contains(&dependency_job_id) {
            self.dependencies.push(dependency_job_id);
        }
    }

    /// 从此任务中移除依赖
    ///
    /// # 参数
    ///
    /// * `dependency_job_id` - 要移除的依赖任务 ID
    pub fn remove_dependency(&mut self, dependency_job_id: &str) {
        self.dependencies.retain(|dep| dep != dependency_job_id);
    }

    /// 检查此任务是否是另一个任务的依赖
    ///
    /// # 参数
    ///
    /// * `other_job` - 其他任务
    ///
    /// # 返回值
    ///
    /// 如果此任务是其他任务的依赖返回 true，否则返回 false
    pub fn is_dependency_for(&self, other_job: &Self) -> bool {
        other_job.depends_on_job(&self.id)
    }

    /// 根据步骤和工作流配置确定任务优先级
    ///
    /// # 参数
    ///
    /// * `step` - 步骤定义
    /// * `workflow` - 工作流定义
    ///
    /// # 返回值
    ///
    /// 返回确定的任务优先级
    fn determine_priority(step: &JobDefinition, workflow: &WorkflowDefinition) -> JobPriority {
        // 将来可以基于以下因素：
        // - 步骤类型（关键步骤获得更高优先级）
        // - 工作流配置
        // - 步骤标签或元数据
        JobPriority::Normal
    }

    /// 将步骤配置应用到任务
    ///
    /// # 参数
    ///
    /// * `job` - 要配置的任务
    /// * `step` - 步骤定义
    /// * `_workflow` - 工作流定义
    ///
    /// # 返回值
    ///
    /// 如果配置成功返回 Ok(())，否则返回错误
    fn apply_step_configuration(
        job: &mut Self,
        step: &JobDefinition,
        _workflow: &WorkflowDefinition,
    ) -> Result<(), CoreError> {
        if let Some(timeout) = step.timeout {
            job.timeout_ms = Some(timeout);
        }

        if let Some(retry) = &step.retry {
            job.retry_config = RetryConfig {
                max_attempts: retry.max_attempts,
                backoff_ms: retry.backoff_ms,
                max_backoff_ms: retry.max_backoff_ms,
            };
        }

        job.add_tag("step_name".to_string(), step.name.clone());
        job.add_tag("step_action".to_string(), step.action.clone());

        Ok(())
    }

    /// 根据步骤配置设置任务依赖关系
    ///
    /// # 参数
    ///
    /// * `job` - 要设置依赖的任务
    /// * `step` - 步骤定义
    /// * `workflow` - 工作流定义
    ///
    /// # 返回值
    ///
    /// 如果设置成功返回 Ok(())，否则返回错误
    fn setup_dependencies(
        job: &mut Self,
        step: &JobDefinition,
        workflow: &WorkflowDefinition,
    ) -> Result<(), CoreError> {
        for dependency_step_id in &step.depends_on {
            if !workflow.steps.iter().any(|s| s.id == *dependency_step_id) {
                return Err(CoreError::InvalidWorkflow(format!(
                    "步骤 '{}' 依赖于不存在的步骤 '{}'",
                    step.id, dependency_step_id
                )));
            }

            let dependency_job_id = format!("{}:{}:{}", workflow.id, job.run_id, dependency_step_id);
            job.dependencies.push(dependency_job_id);
        }

        // 如果没有显式依赖，则依赖于前一个步骤
        if job.dependencies.is_empty() {
            if let Some(step_index) = workflow.steps.iter().position(|s| s.id == step.id) {
                if step_index > 0 {
                    let previous_step = &workflow.steps[step_index - 1];
                    let previous_job_id = format!("{}:{}:{}", workflow.id, job.run_id, previous_step.id);
                    job.dependencies.push(previous_job_id);
                }
            }
        }

        Ok(())
    }

    /// 为任务添加工作流上下文
    ///
    /// # 参数
    ///
    /// * `job` - 要添加上下文的任务
    /// * `workflow` - 工作流定义
    /// * `run` - 工作流运行实例
    ///
    /// # 返回值
    ///
    /// 如果添加成功返回 Ok(())，否则返回错误
    fn add_workflow_context(
        job: &mut Self,
        workflow: &WorkflowDefinition,
        run: &WorkflowRun,
    ) -> Result<(), CoreError> {
        job.add_context("workflow_name".to_string(), serde_json::Value::String(workflow.name.clone()));
        if let Some(description) = &workflow.description {
            job.add_context("workflow_description".to_string(), serde_json::Value::String(description.clone()));
        }
        job.add_context("workflow_created_at".to_string(), serde_json::Value::String(workflow.created_at.to_rfc3339()));
        job.add_context("workflow_updated_at".to_string(), serde_json::Value::String(workflow.updated_at.to_rfc3339()));

        job.add_context("run_started_at".to_string(), serde_json::Value::String(run.started_at.to_rfc3339()));
        job.add_context("run_status".to_string(), serde_json::Value::String(run.status.as_str().to_string()));

        if let Some(step_index) = workflow.steps.iter().position(|s| s.id == job.step_name) {
            job.add_context("step_index".to_string(), serde_json::Value::Number(step_index.into()));
            job.add_context("total_steps".to_string(), serde_json::Value::Number(workflow.steps.len().into()));
        }

        Ok(())
    }

    /// 开始任务执行
    ///
    /// 将任务状态设置为运行中，并记录开始时间
    ///
    /// # 返回值
    ///
    /// 如果启动成功返回 Ok(())，否则返回错误
    pub fn start(&mut self) -> Result<(), CoreError> {
        if self.state != StepStatus::Pending && self.state != StepStatus::Retrying {
            return Err(CoreError::State(
                format!("无法在状态 {:?} 下启动任务", self.state)
            ));
        }

        self.state = StepStatus::Running;
        self.metadata.started_at = Some(Utc::now());
        self.metadata.updated_at = Utc::now();
        self.metadata.attempt_count += 1;

        Ok(())
    }

    /// 成功完成任务
    ///
    /// 将任务状态设置为已完成，并保存执行结果
    ///
    /// # 参数
    ///
    /// * `result` - 步骤执行结果
    ///
    /// # 返回值
    ///
    /// 如果完成成功返回 Ok(())，否则返回错误
    pub fn complete(&mut self, result: JobResult) -> Result<(), CoreError> {
        if self.state != StepStatus::Running {
            return Err(CoreError::State(
                format!("无法在状态 {:?} 下完成任务", self.state)
            ));
        }

        self.state = StepStatus::Completed;
        self.result = Some(result);
        self.metadata.completed_at = Some(Utc::now());
        self.metadata.updated_at = Utc::now();

        Ok(())
    }

    /// 任务执行失败
    ///
    /// 将任务状态设置为失败，并记录错误信息
    ///
    /// # 参数
    ///
    /// * `error` - 错误信息
    ///
    /// # 返回值
    ///
    /// 如果设置失败状态成功返回 Ok(())，否则返回错误
    pub fn fail(&mut self, error: String) -> Result<(), CoreError> {
        if self.state != StepStatus::Running {
            return Err(CoreError::State(
                format!("无法在状态 {:?} 下使任务失败", self.state)
            ));
        }

        self.state = StepStatus::Failed;
        self.metadata.last_error = Some(error);
        self.metadata.completed_at = Some(Utc::now());
        self.metadata.updated_at = Utc::now();

        Ok(())
    }

    /// 重试任务
    ///
    /// 将失败的任务重新设置为重试状态
    ///
    /// # 返回值
    ///
    /// 如果重试设置成功返回 Ok(())，否则返回错误
    pub fn retry(&mut self) -> Result<(), CoreError> {
        if self.state != StepStatus::Failed {
            return Err(CoreError::State(
                format!("无法在状态 {:?} 下重试任务", self.state)
            ));
        }

        if self.metadata.attempt_count >= self.retry_config.max_attempts {
            return Err(CoreError::Configuration(
                "超过最大重试次数".to_string()
            ));
        }

        self.state = StepStatus::Retrying;
        self.metadata.updated_at = Utc::now();

        Ok(())
    }

    /// 取消任务
    ///
    /// 将任务状态设置为已取消
    ///
    /// # 返回值
    ///
    /// 如果取消成功返回 Ok(())，否则返回错误
    pub fn cancel(&mut self) -> Result<(), CoreError> {
        if self.state == StepStatus::Completed || self.state == StepStatus::Failed {
            return Err(CoreError::State(
                format!("无法在状态 {:?} 下取消任务", self.state)
            ));
        }

        self.state = StepStatus::Skipped;
        self.metadata.completed_at = Some(Utc::now());
        self.metadata.updated_at = Utc::now();

        Ok(())
    }

    /// 检查任务是否准备好执行（依赖关系已满足）
    ///
    /// # 参数
    ///
    /// * `completed_jobs` - 已完成任务的 ID 列表
    ///
    /// # 返回值
    ///
    /// 如果任务准备好执行返回 true，否则返回 false
    pub fn is_ready(&self, completed_jobs: &[String]) -> bool {
        if self.state != StepStatus::Pending && self.state != StepStatus::Retrying {
            return false;
        }

        self.dependencies.iter().all(|dep_id| completed_jobs.contains(dep_id))
    }

    /// 检查任务是否已超时
    ///
    /// # 返回值
    ///
    /// 如果任务已超时返回 true，否则返回 false
    pub fn is_timed_out(&self) -> bool {
        if let Some(timeout_ms) = self.timeout_ms {
            if let Some(started_at) = self.metadata.started_at {
                let elapsed = Utc::now().signed_duration_since(started_at);
                return elapsed.num_milliseconds() as u64 > timeout_ms;
            }
        }
        false
    }

    /// 检查任务是否可以重试
    ///
    /// # 返回值
    ///
    /// 如果任务可以重试返回 true，否则返回 false
    pub fn can_retry(&self) -> bool {
        self.state == StepStatus::Failed
            && self.metadata.attempt_count < self.retry_config.max_attempts
    }

    /// 使用指数退避计算下次重试延迟
    ///
    /// 根据重试次数和配置计算下次重试的延迟时间
    ///
    /// # 返回值
    ///
    /// 返回延迟时间（毫秒）
    pub fn next_retry_delay(&self) -> u64 {
        let base_delay = self.retry_config.backoff_ms;
        let attempt = self.metadata.attempt_count.saturating_sub(1);
        let delay = base_delay * 2_u64.pow(attempt);

        // 限制在最大退避时间内
        delay.min(self.retry_config.max_backoff_ms)
    }

    /// 验证任务配置
    ///
    /// 检查任务的各项配置是否有效
    ///
    /// # 返回值
    ///
    /// 如果验证通过返回 Ok(())，否则返回配置错误
    pub fn validate(&self) -> Result<(), CoreError> {
        if self.id.is_empty() {
            return Err(CoreError::Configuration("任务 ID 不能为空".to_string()));
        }

        if self.workflow_id.is_empty() {
            return Err(CoreError::Configuration("工作流 ID 不能为空".to_string()));
        }

        if self.run_id.is_empty() {
            return Err(CoreError::Configuration("运行 ID 不能为空".to_string()));
        }

        if self.step_name.is_empty() {
            return Err(CoreError::Configuration("步骤名称不能为空".to_string()));
        }

        if self.retry_config.max_attempts == 0 {
            return Err(CoreError::Configuration("最大尝试次数必须大于 0".to_string()));
        }

        Ok(())
    }

    /// 为任务添加标签
    ///
    /// # 参数
    ///
    /// * `key` - 标签键
    /// * `value` - 标签值
    pub fn add_tag(&mut self, key: String, value: String) {
        self.metadata.tags.insert(key, value);
        self.metadata.updated_at = Utc::now();
    }

    /// 获取标签值
    ///
    /// # 参数
    ///
    /// * `key` - 标签键
    ///
    /// # 返回值
    ///
    /// 返回标签值的引用，如果不存在则返回 None
    pub fn get_tag(&self, key: &str) -> Option<&String> {
        self.metadata.tags.get(key)
    }

    /// 添加上下文数据
    ///
    /// # 参数
    ///
    /// * `key` - 上下文键
    /// * `value` - 上下文值
    pub fn add_context(&mut self, key: String, value: serde_json::Value) {
        self.context.insert(key, value);
        self.metadata.updated_at = Utc::now();
    }

    /// 获取上下文数据
    ///
    /// # 参数
    ///
    /// * `key` - 上下文键
    ///
    /// # 返回值
    ///
    /// 返回上下文值的引用，如果不存在则返回 None
    pub fn get_context(&self, key: &str) -> Option<&serde_json::Value> {
        self.context.get(key)
    }
}
