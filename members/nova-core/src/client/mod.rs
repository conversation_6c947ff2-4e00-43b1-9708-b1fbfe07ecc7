use std::fmt::Debug;
use serde::Deserialize;
use thiserror::Error;
use crate::completion::completion_client::CompletionClientDyn;

pub mod builder;
pub mod api_model_config;
pub mod simple_chat;
pub mod embedding;
pub mod od;
pub mod simple_chat_client;

pub use builder::*;
pub use api_model_config::*;
pub use crate::completion::completion_client::*;




#[derive(Debug, Deserialize)]
pub struct ApiErrorResponse {
    pub(crate) message: String,
}

#[derive(Debug, Deserialize)]
#[serde(untagged)]
pub(crate) enum ApiResponse<T> {
    Ok(T),
    Err(ApiErrorResponse),
}

/// 尝试将客户端转换为完成大模型调用客户端
pub trait AsCompletion {
    fn as_completion(&self) -> Option<Box<dyn CompletionClientDyn>> {
        None
    }
}

#[derive(Debug, Error)]
#[non_exhaustive]
pub enum ClientBuilderError {
    #[error("reqwest error: {0}")]
    HttpError(
        #[from]
        #[source]
        reqwest::Error,
    ),
    #[error("invalid property: {0}")]
    InvalidProperty(&'static str),
}

pub trait ProviderClient: AsCompletion  + Debug{

    /// 根据传递的认证方法创建客户端
    fn from_val(input: ProviderValue) -> Self
    where
        Self: Sized;

    /// 根据传递的认证方法创建客户端的 Box 类型
    fn from_val_boxed<'a>(input: ProviderValue) -> Box<dyn ProviderClient + 'a>
    where
        Self: Sized,
        Self: 'a,
    {
        Box::new(Self::from_val(input))
    }
}
