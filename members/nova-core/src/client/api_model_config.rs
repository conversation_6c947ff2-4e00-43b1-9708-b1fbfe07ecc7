use serde::{Deserialize, Serialize};


/// 认证方法
#[derive(Debug, Clone, Eq, PartialEq, Hash, Deserialize, Serialize)]
#[serde(tag = "type")]
pub enum AuthMethod {
    BearerToken {
        value: String,
    },
    Api<PERSON><PERSON> {
        key: String,
        value: String,
    },
    BasicAuth {
        username: String,
        password: String,
    },
    LinkerAuth,
    None,
}


///默认值提供者类型
#[derive(Clone)]
pub enum ProviderValue {
    ///只需要请求地址=>内部鉴权
    Simple(String),
    ///请求地址、api key =>第三方算法鉴权
    ApiKey(String, String),
}

impl From<&str> for ProviderValue {
    fn from(value: &str) -> Self {
        Self::Simple(value.to_string())
    }
}

impl From<String> for ProviderValue {
    fn from(value: String) -> Self {
        Self::Simple(value)
    }
}

/// 从(url, api_key)构建ProviderValue
impl<P> From<(P, P)> for ProviderValue
where
    P: AsRef<str>,
{
    fn from((api_key, url): (P, P)) -> Self {
        Self::ApiKey(
            api_key.as_ref().to_string(),
            url.as_ref().to_string(),
        )
    }
}