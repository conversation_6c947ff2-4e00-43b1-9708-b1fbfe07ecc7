use std::convert::Infallible;
use std::str::FromStr;
use serde::{Deserialize, Serialize};
use crate::completion;
use crate::completion::{DocumentSourceKind, ImageDetail};
use crate::one_or_many::OneOrMany;
use crate::one_or_many::string_or_one_or_many;
use crate::util::json_utils::string_or_vec;
use crate::util::json_utils::null_or_vec;
use crate::util::json_utils::stringified_json;

#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
#[serde(tag = "role", rename_all = "lowercase")]
pub enum Message {
    System {
        content: String,
        #[serde(skip_serializing_if = "Option::is_none")]
        name: Option<String>,
    },
    User {
        #[serde(deserialize_with = "string_or_one_or_many")]
        content: OneOrMany<UserContent>,
        #[serde(skip_serializing_if = "Option::is_none")]
        name: Option<String>,
    },
    Assistant {
        #[serde(default, deserialize_with = "string_or_vec")]
        content: Vec<AssistantContent>,
        #[serde(skip_serializing_if = "Option::is_none")]
        refusal: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        name: Option<String>,
        #[serde(
            default,
            deserialize_with = "null_or_vec",
            skip_serializing_if = "Vec::is_empty"
        )]
        tool_calls: Vec<ToolCall>,
    },
    #[serde(rename = "tool")]
    ToolResult {
        tool_call_id: String,
        content: String,
    },
}

impl Message {
    ///创建system消息
    pub fn system(content: &str) -> Self {
        Message::System {
            content: content.to_owned(),
            name: None,
        }
    }
}


impl From<completion::ToolResult> for Message {
    fn from(tool_result: completion::ToolResult) -> Self {
        let content = match tool_result.content.first() {
            completion::ToolResultContent::Text(text) => text.text,
            completion::ToolResultContent::Image(_) => String::from("[Image]"),
        };
        Message::ToolResult {
            tool_call_id: tool_result.id,
            content,
        }
    }
}


///将请求接收的completion::Message转换为Vec<Message>
impl TryFrom<completion::Message> for Vec<Message> {
    type Error = completion::MessageError;

    fn try_from(message: completion::Message) -> Result<Self, Self::Error> {
        match message {
            completion::Message::User { content } => {
                let (tool_results, other_content): (Vec<_>, Vec<_>) = content
                    .into_iter()
                    .partition(|content| matches!(content, completion::UserContent::ToolResult(_)));
                // 工具结果
                if !tool_results.is_empty() {
                    tool_results
                        .into_iter()
                        .map(|content| match content {
                            completion::UserContent::ToolResult(tool_result) => {
                                Some(Message::from(tool_result))
                            }
                            _ => None,
                        })
                        .collect::<Option<Vec<_>>>()
                        .ok_or_else(|| {
                            completion::MessageError::ConversionError(
                                "Tool result content does not support non-text".into(),
                            )
                        })
                } else {
                    let other_content: Vec<UserContent> = other_content.into_iter().map(|content| match content {
                        completion::UserContent::Text(completion::Text { text }) => {
                            Ok(UserContent::Text { text })
                        }
                        completion::UserContent::Image(completion::Image {
                                                        data, detail, ..
                                                    }) => {
                            let DocumentSourceKind::Url(url) = data else { return Err(completion::MessageError::ConversionError(
                                "Only image URL user content is accepted with OpenAI Chat Completions API".to_string()
                            ))};

                            Ok(UserContent::Image {
                                image_url: ImageUrl {
                                    url,
                                    detail: detail.unwrap_or_default(),
                                }
                            }
                            )

                        },
                        _ => unreachable!(),
                    }).collect::<Result<Vec<_>, _>>()?;

                    let other_content = OneOrMany::many(other_content).expect(
                        "There must be other content here if there were no tool result content",
                    );
                    Ok(vec![Message::User {
                        content: other_content,
                        name: None,
                    }])
                }
            }
            completion::Message::Assistant { content, .. } => {
                let mut messages: Vec<Message> = vec![];

                // extract tool calls
                let tool_calls = content
                    .clone()
                    .into_iter()
                    .filter_map(|content| match content {
                        completion::AssistantContent::ToolCall(tool_call) => {
                            Some(ToolCall::from(tool_call))
                        }
                        _ => None,
                    })
                    .collect::<Vec<_>>();

                // if we have tool calls, we add a new Assistant message with them
                if !tool_calls.is_empty() {
                    messages.push(Message::Assistant {
                        content: vec![],
                        refusal: None,
                        name: None,
                        tool_calls,
                    });
                }

                // extract text
                let text_content = content
                    .into_iter()
                    .filter_map(|content| match content {
                        completion::AssistantContent::Text(text) => Some(Message::Assistant {
                            content: vec![AssistantContent::Text { text: text.text }],
                            refusal: None,
                            name: None,
                            tool_calls: vec![],
                        }),
                        _ => None,
                    })
                    .collect::<Vec<_>>();

                messages.extend(text_content);

                Ok(messages)
            }
        }
    }
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
#[serde(tag = "type", rename_all = "lowercase")]
pub enum UserContent {
    Text {
        text: String,
    },
    #[serde(rename = "image_url")]
    Image {
        image_url: ImageUrl,
    },
}

impl From<String> for UserContent {
    fn from(s: String) -> Self {
        UserContent::Text { text: s }
    }
}

impl FromStr for UserContent {
    type Err = Infallible;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(UserContent::Text {
            text: s.to_string(),
        })
    }
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
pub struct ImageUrl {
    pub url: String,
    #[serde(default)]
    pub detail: ImageDetail,
}


#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
#[serde(tag = "type", rename_all = "lowercase")]
pub enum AssistantContent {
    Text { text: String },
    Refusal { refusal: String },
}

impl From<String> for AssistantContent {
    fn from(s: String) -> Self {
        AssistantContent::Text { text: s }
    }
}

impl FromStr for AssistantContent {
    type Err = Infallible;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(AssistantContent::Text {
            text: s.to_string(),
        })
    }
}


#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
pub struct ToolCall {
    pub id: String,
    #[serde(default)]
    pub r#type: ToolType,
    pub function: Function,
}

impl From<completion::ToolCall> for ToolCall {
    fn from(tool_call: completion::ToolCall) -> Self {
        Self {
            id: tool_call.id,
            r#type: ToolType::Function,
            function: Function {
                name: tool_call.function.name,
                arguments: tool_call.function.arguments,
            },
        }
    }
}

#[derive(Default, Debug, Serialize, Deserialize, PartialEq, Clone)]
#[serde(rename_all = "lowercase")]
pub enum ToolType {
    #[default]
    Function,
}


#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
pub struct Function {
    pub name: String,
    #[serde(with = "stringified_json")]
    pub arguments: serde_json::Value,
}