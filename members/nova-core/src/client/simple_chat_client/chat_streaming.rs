use std::collections::HashMap;

use async_stream::stream;
use futures::StreamExt;
use reqwest::RequestBuilder;
use serde::Deserialize;
use serde::Serialize;
use serde_json::json;
use tracing::debug;

use crate::client::simple_chat::CompletionModel;
use crate::client::simple_chat_client::response::Usage;
use crate::completion::CompletionError;
use crate::completion::CompletionRequest;
use crate::completion::GetTokenUsage;
use crate::completion::RawStreamingChoice;
use crate::completion::completion_client;
use crate::util::json_utils;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StreamingFunction {
    #[serde(default)]
    pub name: Option<String>,
    #[serde(default)]
    pub arguments: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct StreamingToolCall {
    pub index: usize,
    pub id: Option<String>,
    pub function: StreamingFunction,
}

#[derive(Deserialize, Debug)]
struct StreamingDelta {
    #[serde(default)]
    content: Option<String>,
    #[serde(default, deserialize_with = "json_utils::null_or_vec")]
    tool_calls: Vec<StreamingToolCall>,
}

#[derive(Deserialize, Debug)]
struct StreamingChoice {
    delta: StreamingDelta,
}

#[derive(Deserialize, Debug)]
struct StreamingCompletionChunk {
    choices: Vec<StreamingChoice>,
    usage: Option<Usage>,
}

#[derive(Clone, Serialize, Deserialize)]
pub struct StreamingCompletionResponse {
    pub usage: Usage,
}

impl GetTokenUsage for StreamingCompletionResponse {
    fn token_usage(&self) -> Option<crate::completion::Usage> {
        let mut usage = crate::completion::Usage::new();
        usage.input_tokens = self.usage.prompt_tokens as u64;
        usage.output_tokens = self.usage.total_tokens as u64 - self.usage.prompt_tokens as u64;
        usage.total_tokens = self.usage.total_tokens as u64;
        Some(usage)
    }
}

pub async fn send_compatible_streaming_request(
    request_builder: RequestBuilder,
) -> Result<
    completion_client::StreamingCompletionResponse<StreamingCompletionResponse>,
    CompletionError,
> {
    //发送请求
    let response = request_builder.send().await?;
    //检查响应状态码
    if !response.status().is_success() {
        return Err(CompletionError::ProviderError(format!(
            "{}: {}",
            response.status(),
            response.text().await?
        )));
    }

    // 处理响应流
    let inner = Box::pin(stream! {
                let mut stream = response.bytes_stream();

                let mut final_usage = Usage {
                    prompt_tokens: 0,
                    total_tokens: 0,
                    completion_tokens: 0,
                };

                let mut partial_data = None;
                 // HashMap<索引, (id, 函数名, 参数字符串)>
                let mut calls: HashMap<usize, (String, String, String)> = HashMap::new();
        //处理流式数据块
                while let Some(chunk_result) = stream.next().await {
                      // 处理可能的网络错误
                    let chunk = match chunk_result {
                        Ok(c) => c,
                        Err(e) => {
                            yield Err(CompletionError::from(e));
                            break;
                        }
                    };
                  // 将字节转换为UTF-8字符串
                    let text = match String::from_utf8(chunk.to_vec()) {
                        Ok(t) => t,
                        Err(e) => {
                            yield Err(CompletionError::ResponseError(e.to_string()));
                            break;
                        }
                    };

                    //逐行处理文本数据
                    for line in text.lines() {
                        let mut line = line.to_string();

                        // 处理跨数据块的不完整行
                        if partial_data.is_some() {
                            // 将之前的部分数据与当前行合并
                            line = format!("{}{}", partial_data.unwrap(), line);
                            partial_data = None;
                        }
                          // 检查是否为SSE (Server-Sent Events) 数据行
                        else {
                            let Some(data) = line.strip_prefix("data:") else {
                                continue;
                            };

                            let data = data.trim_start();
                            // 检查是否为流结束标记
                            if data == "[DONE]" {
                                break
                            }
                            // 处理不完整的JSON数据（跨行分割）
                            if !line.ends_with("}") {
                                partial_data = Some(data.to_string());
                            } else {
                                line = data.to_string();
                            }
                        }
                        //解析JSON数据
                        let data = serde_json::from_str::<StreamingCompletionChunk>(&line);

                        let Ok(data) = data else {
                            let err = data.unwrap_err();
                            debug!("Couldn't serialize data as StreamingCompletionChunk: {:?}", err);
                            continue;
                        };

                        // 处理响应选择
                        if let Some(choice) = data.choices.first() {
                            let delta = &choice.delta;
                            // 处理工具调用
                            if !delta.tool_calls.is_empty() {
                                for tool_call in &delta.tool_calls {
                                    let function = tool_call.function.clone();
                                    //工具调用开始 - 有函数名但没有参数
                                    if function.name.is_some() && function.arguments.is_empty() {
                                        let id = tool_call.id.clone().unwrap_or("".to_string());
                                        calls.insert(tool_call.index, (id, function.name.clone().unwrap(), "".to_string()));
                                    }
                                    //工具调用进行中 - 没有函数名但有参数片段
                                    else if function.name.clone().is_none_or(|s| s.is_empty()) && !function.arguments.is_empty() {
                                        let Some((id, name, arguments)) = calls.get(&tool_call.index) else {
                                            debug!("Partial tool call received but tool call was never started.");
                                            continue;
                                        };
                                        // 累积参数字符串
                                        let new_arguments = &tool_call.function.arguments;
                                        let arguments = format!("{arguments}{new_arguments}");
                                        calls.insert(tool_call.index, (id.clone(), name.clone(), arguments));
                                    }
                                    // 完整的工具调用
                                    else {
                                        let id = tool_call.id.clone().unwrap_or("".to_string());
                                        let name = function.name.expect("function name should be present for complete tool call");
                                        let arguments = function.arguments;
                                      // 解析参数为JSON
                                        let Ok(arguments) = serde_json::from_str(&arguments) else {
                                            debug!("Couldn't serialize '{}' as a json value", arguments);
                                            continue;
                                        };
                                    // 输出完整的工具调用
                                        yield Ok(RawStreamingChoice::ToolCall {id, name, arguments, call_id: None })
                                    }
                                }
                            }
                            // 处理文本内容
                            if let Some(content) = &choice.delta.content {
                                yield Ok(RawStreamingChoice::Message(content.clone()))
                            }
                        }

                        // 更新token使用统计
                        if let Some(usage) = data.usage {
                            final_usage = usage.clone();
                        }
                    }
                }
                //处理剩余的未完成工具调用
                for (_, (id, name, arguments)) in calls {
                    let Ok(arguments) = serde_json::from_str(&arguments) else {
                        continue;
                    };

                    yield Ok(RawStreamingChoice::ToolCall {id, name, arguments, call_id: None });
                }
                //输出最终响应
                yield Ok(RawStreamingChoice::FinalResponse(StreamingCompletionResponse {
                    usage: final_usage.clone()
                }))
            });
    //返回包装后的流式响应
    Ok(completion_client::StreamingCompletionResponse::stream(
        inner,
    ))
}
