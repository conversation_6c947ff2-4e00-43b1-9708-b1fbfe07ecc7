use std::collections::HashMap;
use std::panic::RefUnwindSafe;
use std::panic::UnwindSafe;

use thiserror::Error;

use crate::client::ProviderClient;
use crate::client::api_model_config::AuthMethod;
use crate::completion::CompletionModelDyn;

#[derive(Debug, Error)]
pub enum ClientBuildError {
    #[error("factory error: {}", .0)]
    FactoryError(String),
    #[error("invalid id string: {}", .0)]
    InvalidIdString(String),
    #[error("unsupported feature: {} for {}", .1, .0)]
    UnsupportedFeature(String, String),
    #[error("unknown provider")]
    UnknownProvider,
}

/// 完成大模型调用客户端构建器
pub type BoxCompletionModel<'a> = Box<dyn CompletionModelDyn + 'a>;

pub struct DynClientBuilder {
    registry: HashMap<String, ClientFactory>,
}

impl Default for DynClientBuilder {
    fn default() -> Self {
        Self::new()
    }
}


/// 客户端构建器，支持各种算法类型：llm、embedding、od等
impl<'a> DynClientBuilder {
    /// 生成一个新的实例 `DynClientBuilder`
    pub fn new() -> Self {
        Self {
            registry: HashMap::new(),
        }
            // .register_all(vec![
            //     ClientFactory::new(
            //         DefaultProviders::ANTHROPIC,
            //         anthropic::Client::from_env_boxed,
            //         anthropic::Client::from_val_boxed,
            //     ),
            //
            // ])
    }

    /// 是否为空
    pub fn empty() -> Self {
        Self {
            registry: HashMap::new(),
        }
    }

    /// 注册一个新的客户端工厂
    pub fn register(mut self, client_factory: ClientFactory) -> Self {
        self.registry
            .insert(client_factory.name.clone(), client_factory);
        self
    }

    /// 注册一批客户端工厂
    pub fn register_all(mut self, factories: impl IntoIterator<Item = ClientFactory>) -> Self {
        for factory in factories {
            self.registry.insert(factory.name.clone(), factory);
        }

        self
    }

    /// 构建一个客户端
    pub fn build(&self, provider: &str) -> Result<Box<dyn ProviderClient>, ClientBuildError> {
        let factory = self.get_factory(provider)?;
        factory.build()
    }

    /// 携带认证信息构建客户端
    pub fn build_auth(
        &self,
        provider: &str,
        auth_method: AuthMethod,
    ) -> Result<Box<dyn ProviderClient>, ClientBuildError> {
        let factory = self.get_factory(provider)?;
        factory.build_from_auth(auth_method)
    }



    /// 获取客户端工厂
    fn get_factory(&self, provider: &str) -> Result<&ClientFactory, ClientBuildError> {
        self.registry
            .get(provider)
            .ok_or(ClientBuildError::UnknownProvider)
    }

    ///构建客户端发起llm对话
    pub fn completion(
        &self,
        provider: &str,
        model: &str,
    ) -> Result<BoxCompletionModel<'a>, ClientBuildError> {
        let client = self.build(provider)?;

        let completion = client
            .as_completion()
            .ok_or(ClientBuildError::UnsupportedFeature(
                provider.to_string(),
                "completion".to_owned(),
            ))?;

        Ok(completion.completion_model(model))
    }



    /// 大语言模型特殊处理，需要传递真实调用的模型
    pub fn id<'id>(&'a self, id: &'id str) -> Result<ProviderModelId<'a, 'id>, ClientBuildError> {
        let (provider, model) = self.parse(id)?;
        Ok(ProviderModelId {
            builder: self,
            provider,
            model,
        })
    }

    /// 语法糖支持，例如 `openai:gpt-4o` 会返回 ("openai", "gpt-4o")
    /// 例如 `openai:gpt-4o` 会返回 ("openai", "gpt-4o")
    pub fn parse(&self, id: &'a str) -> Result<(&'a str, &'a str), ClientBuildError> {
        let (provider, model) = id
            .split_once(":")
            .ok_or(ClientBuildError::InvalidIdString(id.to_string()))?;

        Ok((provider, model))
    }
}


pub struct ProviderModelId<'builder, 'id> {
    builder: &'builder DynClientBuilder,
    provider: &'id str,
    model: &'id str,
}

impl<'builder> ProviderModelId<'builder, '_> {
    /// 构建客户端发起llm对话
    pub fn completion(self) -> Result<BoxCompletionModel<'builder>, ClientBuildError> {
        self.builder.completion(self.provider, self.model)
    }

}

/// 客户端工厂: 为不同的算法类型创建客户端
pub struct ClientFactory {
    /// 客户端名称
    pub name: String,

    ///无参数创建客户端
    pub factory_origin: Box<dyn Fn() -> Box<dyn ProviderClient>>,

    /// 从认证方法创建客户端
    pub factory_from_auth: Box<dyn Fn(AuthMethod) -> Box<dyn ProviderClient>>,
}

impl UnwindSafe for ClientFactory {}
impl RefUnwindSafe for ClientFactory {}

impl ClientFactory {
    /// 创建客户端工厂
    pub fn new<F1, F2>(name: &str, func: F1, func_val: F2) -> Self
    where
        F1: 'static + Fn() -> Box<dyn ProviderClient>,
        F2: 'static + Fn(AuthMethod) -> Box<dyn ProviderClient>,
    {
        Self {
            name: name.to_string(),
            factory_origin: Box::new(func),
            factory_from_auth: Box::new(func_val),
        }
    }

    /// 无参数创建客户端
    pub fn build(&self) -> Result<Box<dyn ProviderClient>, ClientBuildError> {
        std::panic::catch_unwind(|| (self.factory_origin)())
            .map_err(|e| ClientBuildError::FactoryError(format!("{e:?}")))
    }

    /// 从认证方法创建客户端
    pub fn build_from_auth(
        &self,
        val: AuthMethod,
    ) -> Result<Box<dyn ProviderClient>, ClientBuildError> {
        std::panic::catch_unwind(|| (self.factory_from_auth)(val))
            .map_err(|e| ClientBuildError::FactoryError(format!("{e:?}")))
    }
}
