use derive_builder::Builder;
use reqwest::Client as HttpClient;
use serde_json::json;
use url::Url;

use crate::client::ApiResponse;
use crate::client::AsCompletion;
use crate::client::CompletionClient;
use crate::client::ProviderClient;
use crate::client::simple_chat_client;
use crate::client::simple_chat_client::{CompletionResponse, Message};
use crate::client::simple_chat_client::chat_streaming::{send_compatible_streaming_request, StreamingCompletionResponse};
use crate::completion;
use crate::completion::CompletionError;
use crate::completion::CompletionRequest;
use crate::completion::ToolDefinition;
use crate::completion::completion_client;
use crate::util::json_utils;
use crate::util::json_utils::merge;


#[derive(Clone, Builder, Debug)]
pub struct Client {
    /// 基础url
    pub url: String,
    /// api key 默认的key
    api_key: String,
    /// 请求客户端
    http_client: HttpClient,
}

impl Client {
    /// 构建客户端
    pub fn new(api_key: impl Into<String>, url: impl Into<String>) -> Self {
        let http_client = reqwest::Client::builder().build();

        ClientBuilder::default()
            .api_key(api_key.into())
            .url(url.into())
            .http_client(http_client.unwrap())
            .build()
            .expect("chat client should build")
    }

    /// 构建post请求
    pub(crate) fn post(&self, path: Option<&str>) -> reqwest::RequestBuilder {
        if let Some(path) = path {
            self.http_client
                .post(path)
                .bearer_auth(&self.api_key)
        } else {
            self.http_client
                .post(self.url.clone())
                .bearer_auth(&self.api_key)
        }
    }
}


impl ProviderClient for Client {
    fn from_val(input: crate::client::ProviderValue) -> Self {
        let crate::client::ProviderValue::ApiKey(url, api_key) = input else {
            panic!("Incorrect provider value type")
        };
        Self::new(&api_key, &url)
    }
}

#[derive(Clone)]
pub struct CompletionModel {
    pub client: Client,
    pub model: String,
}

impl CompletionClient for Client {
    type CompletionModel = CompletionModel;

    /// 构造模型客户端
    fn completion_model(&self, model_name: &str) -> CompletionModel {
        CompletionModel {
            client: self.clone(),
            model: model_name.to_string(),
        }
    }
}

impl CompletionModel {
    /// 创建完成请求
    pub(crate) fn create_completion_request(
        &self,
        completion_request: CompletionRequest,
    ) -> Result<serde_json::Value, CompletionError> {
        let mut partial_history = vec![];
        partial_history.extend(completion_request.chat_history);

        // 系统提示词
        let mut full_history: Vec<Message> = completion_request
            .preamble
            .map_or_else(Vec::new, |preamble| vec![Message::system(&preamble)]);

        // 转换并扩展剩余的历史记录
        full_history.extend(
            partial_history
                .into_iter()
                .map(completion::Message::try_into)
                .collect::<Result<Vec<Vec<Message>>, _>>()?
                .into_iter()
                .flatten()
                .collect::<Vec<_>>(),
        );

        let request = if completion_request.tools.is_empty() {
            json!({
                "model": self.model,
                "messages": full_history,
                "temperature": completion_request.temperature,
            })
        } else {
            json!({
                "model": self.model,
                "messages": full_history,
                "temperature": completion_request.temperature,
                "tools": completion_request.tools.into_iter().map(ToolDefinition::from).collect::<Vec<_>>(),
                "tool_choice": "auto",
            })
        };

        let request = if let Some(params) = completion_request.additional_params {
            json_utils::merge(request, params)
        } else {
            request
        };

        Ok(request)
    }
}

///实现大模型的completion
impl completion_client::CompletionModel for CompletionModel {
    type Response = CompletionResponse;
    type StreamingResponse = StreamingCompletionResponse;

    async fn completion(
        &self,
        completion_request: CompletionRequest,
    ) -> Result<completion_client::CompletionResponse<CompletionResponse>, CompletionError> {
        let request = self.create_completion_request(completion_request)?;

        tracing::debug!("DeepSeek completion request: {request:?}");

        let response = self.client.post(None).json(&request).send().await?;

        if response.status().is_success() {
            let t = response.text().await?;
            tracing::debug!(target: "rig", "DeepSeek completion: {}", t);

            match serde_json::from_str::<ApiResponse<CompletionResponse>>(&t)? {
                ApiResponse::Ok(response) => response.try_into(),
                ApiResponse::Err(err) => Err(CompletionError::ProviderError(err.message)),
            }
        } else {
            Err(CompletionError::ProviderError(response.text().await?))
        }
    }

    async fn stream(
        &self,
        completion_request: CompletionRequest,
    ) -> Result<completion_client::StreamingCompletionResponse<Self::StreamingResponse>, CompletionError>
    {
        let mut request = self.create_completion_request(completion_request)?;

        request = merge(
            request,
            json!({"stream": true, "stream_options": {"include_usage": true}}),
        );

        let builder = self.client.post(None).json(&request);
        send_compatible_streaming_request(builder).await
    }
}
