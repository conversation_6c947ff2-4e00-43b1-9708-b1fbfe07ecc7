use std::collections::HashMap;

use serde::Deserialize;
use serde::Serialize;

///linker 认证url信息配置
#[derive(Debug, Default, <PERSON>lone, Deserialize, Serialize)]
pub struct CheckServingAuth {
    pub url: String,
}

#[derive(Debug, De<PERSON>ult, <PERSON>lone, Deserialize, Serialize)]
pub struct LinkerAbilityRequestExtra {
    #[serde(rename = "checkServingDto")]
    pub check_serving_dto: CheckServingAuth,

    ///debug 标识
    pub linker_ai_debug_flag: String,

    ///额外参数：
    pub extra: serde_json::Value,
}

#[derive(Debug, Default, <PERSON>lone, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct LinkerAbilityRequest {
    ///任务标识
    pub task_id: String,

    ///请求详细信息
    pub src: Vec<LinkerAbilityRequestExtra>,

    ///linker 能力请求额外参数
    pub scheduling_center: LinkerAbilityRequestExtra,
}

#[derive(Debug, Default, <PERSON>lone, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct LinkerAbilityRequestItem {
    ///数据信息
    pub data: String,

    ///数据类型
    pub src_type: String,

    ///数据id
    pub image_id: String,

    ///数据归属视频id
    pub video_id: String,

    ///事件时间
    pub event_time: String,

    ///额外参数
    pub kwargs: serde_json::Value,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct LinkerAbilityResponse {
    ///状态码
    pub code: i32,

    ///是否成功
    pub is_success: bool,
    ///响应体
    pub body: RespBody,

    /// 详情响应体
    pub results: HashMap<String, Vec<ItemResponse>>,

    ///linker 能力请求额外参数
    pub scheduling_center: LinkerAbilityRequestExtra,
}

/// 详情响应体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ItemResponse {
    /// 对应传参imageId
    pub id: String,
    /// ai返回的图片（已标注图片）
    pub image: String,
    /// ai返回的描述
    pub description: String,
    /// 是否正常 false标识发现异常  true 标识正常
    pub status: Option<bool>,
    /// 框的坐标
    pub bbox_list: Vec<BBoxItem>,
    /// 额外参数
    pub kwargs: serde_json::Value,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RespBody {
    message: String,
    low_code_stdout: String,
    took: u64,
    #[serde(rename = "taskId")]
    task_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BBoxItem {
    /// 标签
    pub label: Vec<String>,
    /// 坐标
    pub bbox: [f64; 4],
    /// 置信度
    pub conf: Vec<String>,
    /// 属性
    pub attribute: Vec<Attribute>,
    /// 状态
    pub status: Option<bool>,
    /// 选区id
    pub region_id: Option<i32>,
    /// 是否为陌生人（人脸是否为陌生人，True为陌生人，False为在底库有录入记录的人）
    pub stranger: Option<bool>,

    /// 匹配id:比对算法用
    pub match_id: Option<String>,

    /// 匹配置信度：比对算法使用
    pub match_conf: Option<String>,
}

/// Attribute 结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Attribute {
    /// 属性置信度
    pub conf: String,
    /// 属性名称
    pub name: String,
}
