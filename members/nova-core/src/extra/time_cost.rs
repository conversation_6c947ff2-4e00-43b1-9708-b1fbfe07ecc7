use std::pin::Pin;
use std::task::Context;
use std::task::Poll;
use std::time::Duration;
use std::time::Instant;

use futures::Future;
use pin_project_lite::pin_project;
///用于测量 Future 执行时间的工具模块
pub trait TimedExt: Sized + Future {
    fn timed<F>(self, f: F) -> Timed<Self, F>
    where
        F: FnMut(&Self::Output, Duration),
    {
        Timed {
            inner: self,
            f,
            start: None,
        }
    }
}

pin_project! {
pub struct Timed<Fut, F>
where
    Fut: Future,
    F: FnMut(&Fut::Output, Duration),
{
    #[pin]
    inner: Fut,
    f: F,
    start: Option<Instant>,
}
    }

impl<Fut, F> Future for Timed<Fut, F>
where
    Fut: Future,
    F: FnMut(&Fut::Output, Duration),
{
    type Output = Fut::Output;

    fn poll(self: Pin<&mut Self>, cx: &mut Context<'_>) -> std::task::Poll<Self::Output> {
        let this = self.project();

        match this.start {
            Some(start) => match this.inner.poll(cx) {
                Poll::Ready(output) => {
                    let elapsed = start.elapsed();
                    //调用回调函数，将执行时间传递给回调函数
                    (this.f)(&output, elapsed);
                    Poll::Ready(output)
                }
                Poll::Pending => Poll::Pending,
            },
            None => {
                *this.start = Some(Instant::now());
                // Continue polling after setting the start time.
                cx.waker().wake_by_ref();
                Poll::Pending
            }
        }
    }
}

///所有的异步任务都实现了TimedExt trait
impl<F: Future> TimedExt for F {}
