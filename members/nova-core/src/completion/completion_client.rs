use crate::agent::AgentBuilder;
use crate::client::{AsCompletion, ProviderClient};
// use crate::completion::ExtractorBuilder;
use crate::completion::GetTokenUsage;
pub(crate) use crate::completion::{CompletionError, CompletionModel, CompletionModelDyn, CompletionRequest, CompletionResponse, StreamingCompletionResponse};
use std::sync::Arc;

pub trait CompletionClient: ProviderClient + Clone {
    /// 大语言模型
    type CompletionModel: CompletionModel;

    ///通过模型名称创建大语言模型调用处理器
    fn completion_model(&self, model: &str) -> Self::CompletionModel;

    /// Create an agent builder with the given completion model.
    fn agent(&self, model: &str) -> AgentBuilder<Self::CompletionModel> {
        AgentBuilder::new(self.completion_model(model))
    }

    // /// 通过模型名称创建提取器构建器
    // fn extractor<T: JsonSchema + for<'a> Deserialize<'a> + Serialize + Send + Sync>(
    //     &self,
    //     model: &str,
    // ) -> ExtractorBuilder<Self::CompletionModel, T> {
    //     ExtractorBuilder::new(self.completion_model(model))
    // }
}

/// 完成模型调用客户端
pub trait CompletionClientDyn: ProviderClient {
    /// 创建模型调用处理器
    fn completion_model<'a>(&self, model: &str) -> Box<dyn CompletionModelDyn + 'a>;

    /// 创建agent调用处理器
    fn agent<'a>(&self, model: &str) -> AgentBuilder<CompletionModelHandle<'a>>;
}

impl<T, M, R> CompletionClientDyn for T
where
    T: CompletionClient<CompletionModel = M>,
    M: CompletionModel<StreamingResponse = R> + 'static,
    R: Clone + Unpin + GetTokenUsage + 'static,
{
    fn completion_model<'a>(&self, model: &str) -> Box<dyn CompletionModelDyn + 'a> {
        Box::new(self.completion_model(model))
    }

    fn agent<'a>(&self, model: &str) -> AgentBuilder<CompletionModelHandle<'a>> {
        AgentBuilder::new(CompletionModelHandle {
            inner: Arc::new(self.completion_model(model)),
        })
    }
}

impl<T> AsCompletion for T
where
    T: CompletionClientDyn + Clone + 'static,
{
    fn as_completion(&self) -> Option<Box<dyn CompletionClientDyn>> {
        Some(Box::new(self.clone()))
    }
}



/// 包装完成模型调用处理器
#[derive(Clone)]
pub struct CompletionModelHandle<'a> {
    pub inner: Arc<dyn CompletionModelDyn + 'a>,
}

impl CompletionModel for CompletionModelHandle<'_> {
    type Response = ();
    type StreamingResponse = ();

    fn completion(
        &self,
        request: CompletionRequest,
    ) -> impl Future<Output = Result<CompletionResponse<Self::Response>, CompletionError>> + Send
    {
        self.inner.completion(request)
    }

    fn stream(
        &self,
        request: CompletionRequest,
    ) -> impl Future<
        Output = Result<StreamingCompletionResponse<Self::StreamingResponse>, CompletionError>,
    > + Send {
        self.inner.stream(request)
    }
}