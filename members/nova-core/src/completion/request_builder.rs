use crate::completion::error::CompletionError;
use crate::completion::interface::CompletionModel;
use crate::completion::CompletionRequest;
use crate::completion::CompletionResponse;
use crate::completion::Message;
use crate::completion::StreamingCompletionResponse;
use crate::completion::ToolDefinition;
use crate::one_or_many::OneOrMany;
use crate::util::json_utils;

/// 完成请求构建器
///
/// 该结构体用于构建完成请求
pub struct CompletionRequestBuilder<M: CompletionModel> {
    model: M,
    ///用户输入
    prompt: Message,
    /// 系统提示
    preamble: Option<String>,
    /// 对话历史
    chat_history: Vec<Message>,
    /// 工具
    tools: Vec<ToolDefinition>,
    /// 温度
    temperature: Option<f64>,
    /// 最大令牌数
    max_tokens: Option<u64>,
    /// 额外参数
    additional_params: Option<serde_json::Value>,
}

impl<M: CompletionModel> CompletionRequestBuilder<M> {
    /// 创建一个完成请求构建器
    pub fn new(model: M, prompt: impl Into<Message>) -> Self {
        Self {
            model,
            prompt: prompt.into(),
            preamble: None,
            chat_history: Vec::new(),
            tools: Vec::new(),
            temperature: None,
            max_tokens: None,
            additional_params: None,
        }
    }

    /// 设置系统提示
    pub fn preamble(mut self, preamble: String) -> Self {
        self.preamble = Some(preamble);
        self
    }

    /// 移除系统提示
    pub fn without_preamble(mut self) -> Self {
        self.preamble = None;
        self
    }

    /// 添加一条消息到对话历史
    pub fn message(mut self, message: Message) -> Self {
        self.chat_history.push(message);
        self
    }

    /// 添加多条消息到对话历史
    pub fn messages(self, messages: Vec<Message>) -> Self {
        messages
            .into_iter()
            .fold(self, |builder, msg| builder.message(msg))
    }

    /// 添加工具
    pub fn tool(mut self, tool: ToolDefinition) -> Self {
        self.tools.push(tool);
        self
    }

    /// 添加多个工具
    pub fn tools(self, tools: Vec<ToolDefinition>) -> Self {
        tools
            .into_iter()
            .fold(self, |builder, tool| builder.tool(tool))
    }

    /// 添加额外参数
    pub fn additional_params(mut self, additional_params: serde_json::Value) -> Self {
        match self.additional_params {
            Some(params) => {
                self.additional_params = Some(json_utils::merge(params, additional_params));
            }
            None => {
                self.additional_params = Some(additional_params);
            }
        }
        self
    }

    /// 设置额外参数
    pub fn additional_params_opt(mut self, additional_params: Option<serde_json::Value>) -> Self {
        self.additional_params = additional_params;
        self
    }

    /// 设置temperature
    pub fn temperature(mut self, temperature: f64) -> Self {
        self.temperature = Some(temperature);
        self
    }

    /// 设置temperature
    pub fn temperature_opt(mut self, temperature: Option<f64>) -> Self {
        self.temperature = temperature;
        self
    }

    /// 设置最大令牌数
    pub fn max_tokens(mut self, max_tokens: u64) -> Self {
        self.max_tokens = Some(max_tokens);
        self
    }

    ///设置最大令牌数
    pub fn max_tokens_opt(mut self, max_tokens: Option<u64>) -> Self {
        self.max_tokens = max_tokens;
        self
    }

    /// 构建完成请求
    pub fn build(self) -> CompletionRequest {
        let chat_history = OneOrMany::many([self.chat_history, vec![self.prompt]].concat())
            .expect("There will always be at least the prompt");

        CompletionRequest {
            preamble: self.preamble,
            chat_history,
            tools: self.tools,
            temperature: self.temperature,
            max_tokens: self.max_tokens,
            additional_params: self.additional_params,
        }
    }

    /// 发送完成请求
    pub async fn send(self) -> Result<CompletionResponse<M::Response>, CompletionError> {
        let model = self.model.clone();
        model.completion(self.build()).await
    }

    /// 发送流式完成请求
    pub async fn stream<'a>(
        self,
    ) -> Result<StreamingCompletionResponse<M::StreamingResponse>, CompletionError>
    where
        <M as CompletionModel>::StreamingResponse: 'a,
        Self: 'a,
    {
        let model = self.model.clone();
        model.stream(self.build()).await
    }
}
