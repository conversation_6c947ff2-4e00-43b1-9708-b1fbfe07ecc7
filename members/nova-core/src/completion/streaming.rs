use std::pin::Pin;
use std::sync::atomic::AtomicBool;
use std::task::Context;
use std::task::Poll;

use futures::Stream;
use futures::StreamExt;
use futures::stream::AbortHandle;
use futures::stream::Abortable;
use tokio::sync::watch;

use crate::completion::{AssistantContent, StreamingCompletionResponse};
use crate::completion::CompletionResponse;
use crate::completion::Message;
use crate::completion::Reasoning;
use crate::completion::StreamedAssistantContent;
use crate::completion::ToolCall;
use crate::completion::ToolFunction;
use crate::completion::error::CompletionError;
use crate::completion::interface::CompletionModel;
use crate::completion::request_builder::CompletionRequestBuilder;
use crate::completion::usage::GetTokenUsage;
use crate::completion::usage::Usage;
use crate::one_or_many::OneOrMany;

/// 暂停恢复处理器
pub struct PauseControl {
    pub(crate) paused_tx: watch::Sender<bool>,
    pub(crate) paused_rx: watch::Receiver<bool>,
}

impl PauseControl {
    pub fn new() -> Self {
        let (paused_tx, paused_rx) = watch::channel(false);
        Self {
            paused_tx,
            paused_rx,
        }
    }

    /// 暂停
    pub fn pause(&self) {
        self.paused_tx.send(true).unwrap();
    }

    /// 恢复
    pub fn resume(&self) {
        self.paused_tx.send(false).unwrap();
    }

    /// 是否暂停
    pub fn is_paused(&self) -> bool {
        *self.paused_rx.borrow()
    }
}

impl Default for PauseControl {
    fn default() -> Self {
        Self::new()
    }
}

///  streaming chunk 类型
#[derive(Debug, Clone)]
pub enum RawStreamingChoice<R>
where
    R: Clone,
{
    /// text
    Message(String),

    /// 工具调用
    ToolCall {
        id: String,
        call_id: Option<String>,
        name: String,
        arguments: serde_json::Value,
    },
    ///思考过程
    Reasoning {
        id: Option<String>,
        reasoning: String,
    },

    FinalResponse(R),
}

/// 流式助手响应结果
pub type StreamingResult<R> =
    Pin<Box<dyn Stream<Item = Result<RawStreamingChoice<R>, CompletionError>> + Send>>;



impl<R> StreamingCompletionResponse<R>
where
    R: Clone + Unpin + GetTokenUsage,
{
    ///流式结果转换
    pub fn stream(inner: StreamingResult<R>) -> StreamingCompletionResponse<R> {
        let (abort_handle, abort_registration) = AbortHandle::new_pair();
        let abort_able_stream = Abortable::new(inner, abort_registration);
        let pause_control = PauseControl::new();
        Self {
            inner: abort_able_stream,
            abort_handle,
            pause_control,
            reasoning: String::new(),
            text: "".to_string(),
            tool_calls: vec![],
            choice: OneOrMany::one(AssistantContent::text("")),
            response: None,
            final_response_yielded: AtomicBool::new(false),
        }
    }

    /// 取消
    pub fn cancel(&self) {
        self.abort_handle.abort();
    }

    /// 暂停
    pub fn pause(&self) {
        self.pause_control.pause();
    }

    /// 恢复
    pub fn resume(&self) {
        self.pause_control.resume();
    }

    /// 是否暂停
    pub fn is_paused(&self) -> bool {
        self.pause_control.is_paused()
    }
}

/// 从流式助手响应转换为助手响应
/// 注意：如果流式助手响应没有最终响应，那么最终响应将为None
/// 注意：如果流式助手响应有最终响应，那么最终响应将被包含在StreamingCompletionResponse中
impl<R> From<StreamingCompletionResponse<R>> for CompletionResponse<Option<R>>
where
    R: Clone + Unpin + GetTokenUsage,
{
    fn from(value: StreamingCompletionResponse<R>) -> CompletionResponse<Option<R>> {
        CompletionResponse {
            choice: value.choice,
            usage: Usage::new(), // Usage is not tracked in streaming responses
            raw_response: value.response,
        }
    }
}

/// 流式助手响应流
impl<R> Stream for StreamingCompletionResponse<R>
where
    R: Clone + Unpin + GetTokenUsage,
{
    type Item = Result<StreamedAssistantContent<R>, CompletionError>;
    ///拉取消息
    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        let stream = self.get_mut();

        // 暂停时，等待恢复
        if stream.is_paused() {
            cx.waker().wake_by_ref();
            return Poll::Pending;
        }

        match Pin::new(&mut stream.inner).poll_next(cx) {
            Poll::Pending => Poll::Pending,
            Poll::Ready(None) => {
                // This is run at the end of the inner stream to collect all tokens into
                // a single unified `Message`.
                let mut choice = vec![];

                stream.tool_calls.iter().for_each(|tc| {
                    choice.push(AssistantContent::ToolCall(tc.clone()));
                });

                // This is required to ensure there's always at least one item in the content
                if choice.is_empty() || !stream.text.is_empty() {
                    choice.insert(0, AssistantContent::text(stream.text.clone()));
                }

                stream.choice = OneOrMany::many(choice)
                    .expect("There should be at least one assistant message");

                Poll::Ready(None)
            }
            Poll::Ready(Some(Err(err))) => {
                if matches!(err, CompletionError::ProviderError(ref e) if e.to_string().contains("aborted"))
                {
                    return Poll::Ready(None); // Treat cancellation as stream termination
                }
                Poll::Ready(Some(Err(err)))
            }
            Poll::Ready(Some(Ok(choice))) => match choice {
                RawStreamingChoice::Message(text) => {
                    // Forward the streaming tokens to the outer stream
                    // and concat the text together
                    stream.text = format!("{}{}", stream.text, text.clone());
                    Poll::Ready(Some(Ok(StreamedAssistantContent::text(&text))))
                }
                RawStreamingChoice::Reasoning { id, reasoning } => {
                    // Forward the streaming tokens to the outer stream
                    // and concat the text together
                    stream.reasoning = format!("{}{}", stream.reasoning, reasoning.clone());
                    Poll::Ready(Some(Ok(StreamedAssistantContent::Reasoning(Reasoning {
                        id,
                        reasoning: vec![stream.reasoning.clone()],
                    }))))
                }
                RawStreamingChoice::ToolCall {
                    id,
                    name,
                    arguments,
                    call_id,
                } => {
                    // Keep track of each tool call to aggregate the final message later
                    // and pass it to the outer stream
                    stream.tool_calls.push(ToolCall {
                        id: id.clone(),
                        call_id: call_id.clone(),
                        function: ToolFunction {
                            name: name.clone(),
                            arguments: arguments.clone(),
                        },
                    });
                    if let Some(call_id) = call_id {
                        Poll::Ready(Some(Ok(StreamedAssistantContent::tool_call_with_call_id(
                            id, call_id, name, arguments,
                        ))))
                    } else {
                        Poll::Ready(Some(Ok(StreamedAssistantContent::tool_call(
                            id, name, arguments,
                        ))))
                    }
                }
                RawStreamingChoice::FinalResponse(response) => {
                    if stream
                        .final_response_yielded
                        .load(std::sync::atomic::Ordering::SeqCst)
                    {
                        stream.poll_next_unpin(cx)
                    } else {
                        // Set the final response field and return the next item in the stream
                        stream.response = Some(response.clone());
                        stream
                            .final_response_yielded
                            .store(true, std::sync::atomic::Ordering::SeqCst);
                        let final_response = StreamedAssistantContent::final_response(response);
                        Poll::Ready(Some(Ok(final_response)))
                    }
                }
            },
        }
    }
}

// /// Trait for high-level streaming prompt interface
// pub trait StreamingPrompt<M, R>
// where
//     M: CompletionModel + 'static,
//     <M as CompletionModel>::StreamingResponse: Send,
//     R: Clone + Unpin + GetTokenUsage,
// {
//     /// Stream a simple prompt to the model
//     fn stream_prompt(&self, prompt: impl Into<Message> + Send) -> StreamingPromptRequest<M, ()>;
// }

// /// Trait for high-level streaming chat interface
// pub trait StreamingChat<M, R>: Send + Sync
// where
//     M: CompletionModel + 'static,
//     <M as CompletionModel>::StreamingResponse: Send,
//     R: Clone + Unpin + GetTokenUsage,
// {
//     /// Stream a chat with history to the model
//     fn stream_chat(
//         &self,
//         prompt: impl Into<Message> + Send,
//         chat_history: Vec<Message>,
//     ) -> StreamingPromptRequest<M, ()>;
// }

/// 流式助手
pub trait StreamingCompletion<M: CompletionModel> {
    ///流失请求构建
    fn stream_completion(
        &self,
        prompt: impl Into<Message> + Send,
        chat_history: Vec<Message>,
    ) -> impl Future<Output = Result<CompletionRequestBuilder<M>, CompletionError>>;
}

pub(crate) struct StreamingResultDyn<R: Clone + Unpin> {
    pub(crate) inner: StreamingResult<R>,
}

impl<R: Clone + Unpin> Stream for StreamingResultDyn<R> {
    type Item = Result<RawStreamingChoice<()>, CompletionError>;

    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        let stream = self.get_mut();

        match stream.inner.as_mut().poll_next(cx) {
            Poll::Pending => Poll::Pending,
            Poll::Ready(None) => Poll::Ready(None),
            Poll::Ready(Some(Err(err))) => Poll::Ready(Some(Err(err))),
            Poll::Ready(Some(Ok(chunk))) => match chunk {
                RawStreamingChoice::FinalResponse(_) => {
                    Poll::Ready(Some(Ok(RawStreamingChoice::FinalResponse(()))))
                }
                RawStreamingChoice::Message(m) => {
                    Poll::Ready(Some(Ok(RawStreamingChoice::Message(m))))
                }
                RawStreamingChoice::Reasoning { id, reasoning } => {
                    Poll::Ready(Some(Ok(RawStreamingChoice::Reasoning { id, reasoning })))
                }
                RawStreamingChoice::ToolCall {
                    id,
                    name,
                    arguments,
                    call_id,
                } => Poll::Ready(Some(Ok(RawStreamingChoice::ToolCall {
                    id,
                    name,
                    arguments,
                    call_id,
                }))),
            },
        }
    }
}
//
// /// helper function to stream a completion request to stdout
// pub async fn stream_to_stdout<M>(
//     agent: &Agent<M>,
//     stream: &mut StreamingCompletionResponse<M::StreamingResponse>,
// ) -> Result<(), std::io::Error>
// where
//     M: CompletionModel,
// {
//     let mut is_reasoning = false;
//     print!("Response: ");
//     while let Some(chunk) = stream.next().await {
//         match chunk {
//             Ok(StreamedAssistantContent::Text(text)) => {
//                 if is_reasoning {
//                     is_reasoning = false;
//                     println!("\n---\n");
//                 }
//                 print!("{}", text.text);
//                 std::io::Write::flush(&mut std::io::stdout())?;
//             }
//             Ok(StreamedAssistantContent::ToolCall(tool_call)) => {
//                 let res = agent
//                     .tools
//                     .call(
//                         &tool_call.function.name,
//                         tool_call.function.arguments.to_string(),
//                     )
//                     .await
//                     .map_err(|e| std::io::Error::other(e.to_string()))?;
//                 println!("\nResult: {res}");
//             }
//             Ok(StreamedAssistantContent::Final(res)) => {
//                 let json_res = serde_json::to_string_pretty(&res).unwrap();
//                 println!();
//                 tracing::info!("Final result: {json_res}");
//             }
//             Ok(StreamedAssistantContent::Reasoning(Reasoning { reasoning, .. })) => {
//                 if !is_reasoning {
//                     is_reasoning = true;
//                     println!();
//                     println!("Thinking: ");
//                 }
//                 let reasoning = reasoning.into_iter().collect::<Vec<String>>().join("");
//
//                 print!("{reasoning}");
//                 std::io::Write::flush(&mut std::io::stdout())?;
//             }
//             Err(e) => {
//                 if e.to_string().contains("aborted") {
//                     println!("\nStream cancelled.");
//                     break;
//                 }
//                 eprintln!("Error: {e}");
//                 break;
//             }
//         }
//     }
//
//     println!(); // New line after streaming completes
//
//     Ok(())
// }
