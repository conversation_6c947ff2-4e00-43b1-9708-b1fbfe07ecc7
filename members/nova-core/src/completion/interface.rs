use std::sync::Arc;
use futures::future::BoxFuture;
use serde::de::DeserializeOwned;
use serde::Serialize;
use crate::client::CompletionModelHandle;
use crate::completion::{streaming, CompletionError, CompletionRequest, CompletionRequestBuilder, CompletionResponse, GetTokenUsage, Message, StreamingCompletionResponse};


///快捷
pub trait Completion<M: CompletionModel> {
    /// 传递提示和聊天历史记录，返回一个完成请求构建器
    fn completion(
        &self,
        prompt: impl Into<Message> + Send,
        chat_history: Vec<Message>,
    ) -> impl std::future::Future<Output = Result<CompletionRequestBuilder<M>, CompletionError>> + Send;
}


///大模型调用接口
///
/// 该 trait 定义了大模型调用的基本接口，包括同步调用和流式调用。
///
/// 实现该 trait 的类型可以是大模型的客户端，也可以是大模型的代理。
pub trait CompletionModel: Clone + Send + Sync {
    /// 模型响应类型
    type Response: Send + Sync + Serialize + DeserializeOwned;
    /// 流式响应类型
    type StreamingResponse: Clone
    + Unpin
    + Send
    + Sync
    + Serialize
    + DeserializeOwned
    + GetTokenUsage;


    /// 该方法用于同步调用大模型，返回一个完成响应。
    fn completion(
        &self,
        request: CompletionRequest,
    ) -> impl std::future::Future<
        Output = Result<CompletionResponse<Self::Response>, CompletionError>,
    > + Send;


    /// 该方法用于流式调用大模型，返回一个流式响应。
    fn stream(
        &self,
        request: CompletionRequest,
    ) -> impl std::future::Future<
        Output = Result<StreamingCompletionResponse<Self::StreamingResponse>, CompletionError>,
    > + Send;

    /// 该方法用于创建一个完成请求构建器，用于构建完成请求。
    fn completion_request(&self, prompt: impl Into<Message>) -> CompletionRequestBuilder<Self> {
        CompletionRequestBuilder::new(self.clone(), prompt)
    }
}





pub trait  CompletionModelDyn: Send + Sync {

    /// 传递完成请求，返回一个完成响应
    fn completion(
        &self,
        request: CompletionRequest,
    ) -> BoxFuture<'_, Result<CompletionResponse<()>, CompletionError>>;

    /// 传递完成请求，返回一个流式响应
    fn stream(
        &self,
        request: CompletionRequest,
    ) -> BoxFuture<'_, Result<StreamingCompletionResponse<()>, CompletionError>>;

    /// 创建一个完成请求构建器
    fn completion_request(
        &self,
        prompt: Message,
    ) -> CompletionRequestBuilder<CompletionModelHandle<'_>>;
}



impl<T, R> CompletionModelDyn for T
where
    T: CompletionModel<StreamingResponse = R>,
    R: Clone + Unpin + GetTokenUsage + 'static,
{
    fn completion(
        &self,
        request: CompletionRequest,
    ) -> BoxFuture<'_, Result<CompletionResponse<()>, CompletionError>> {
        Box::pin(async move {
            self.completion(request)
                .await
                .map(|resp| CompletionResponse {
                    choice: resp.choice,
                    usage: resp.usage,
                    raw_response: (),
                })
        })
    }

    fn stream(
        &self,
        request: CompletionRequest,
    ) -> BoxFuture<'_, Result<StreamingCompletionResponse<()>, CompletionError>> {
        Box::pin(async move {
            let resp = self.stream(request).await?;
            let inner = resp.inner;

            let stream = Box::pin(streaming::StreamingResultDyn {
                inner: Box::pin(inner),
            });

            Ok(StreamingCompletionResponse::stream(stream))
        })
    }

    /// Generates a completion request builder for the given `prompt`.
    fn completion_request(
        &self,
        prompt: Message,
    ) -> CompletionRequestBuilder<CompletionModelHandle<'_>> {
        CompletionRequestBuilder::new(
            CompletionModelHandle {
                inner: Arc::new(self.clone()),
            },
            prompt,
        )
    }
}