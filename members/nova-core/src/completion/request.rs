use crate::completion::{<PERSON><PERSON>ontent, GetTokenUsage, Message, PauseControl, StreamingResult, ToolCall, ToolDefinition, Usage};
use crate::one_or_many::OneOrMany;
use futures::future::{AbortHandle, Abortable};
use std::sync::atomic::AtomicBool;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone)]
pub struct CompletionRequest {
    /// 提示
    pub preamble: Option<String>,
    /// 聊天历史
    /// 最后一条消息将始终是提示（因此总是有一条）
    pub chat_history: OneOrMany<Message>,
    /// 工具
    pub tools: Vec<ToolDefinition>,
    /// 温度
    pub temperature: Option<f64>,
    /// 最大令牌数
    pub max_tokens: Option<u64>,
    /// 额外的供应商特定参数,会直接放入到请求体中
    pub additional_params: Option<serde_json::Value>,
}


#[derive(Debug,Serialize,Deserialize)]
pub struct CompletionResponse<T> {
    /// 助手内容
    pub choice: OneOrMany<AssistantContent>,
    /// token 统计
    pub usage: Usage,
    /// 原始响应
    pub raw_response: T,
}


/// 流式助手响应
pub struct StreamingCompletionResponse<R>
where
    R: Clone + Unpin + GetTokenUsage,
{
    pub(crate) inner: Abortable<StreamingResult<R>>,
    pub(crate) abort_handle: AbortHandle,
    pub(crate) pause_control: PauseControl,
    pub(crate) text: String,
    pub(crate) reasoning: String,
    pub(crate) tool_calls: Vec<ToolCall>,
    /// 助手输出内容
    pub choice: OneOrMany<AssistantContent>,
    /// 最终响应
    pub response: Option<R>,
    pub final_response_yielded: AtomicBool,
}