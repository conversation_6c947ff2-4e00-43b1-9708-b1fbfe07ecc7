use std::ops::Add;
use std::ops::AddAssign;

use serde::Deserialize;
use serde::Serialize;

/// 获取token用量
pub trait GetTokenUsage {
    fn token_usage(&self) -> Option<Usage>;
}

impl GetTokenUsage for () {
    fn token_usage(&self) -> Option<Usage> {
        None
    }
}

/// 用量统计
#[derive(Debug, PartialEq, Eq, Clone, Copy, Serialize, Deserialize)]
pub struct Usage {
    /// 输入token数量
    pub input_tokens: u64,
    /// 输出token数量
    pub output_tokens: u64,
    /// 总token数量
    pub total_tokens: u64,
}

impl Usage {
    ///初始化
    pub fn new() -> Self {
        Self {
            input_tokens: 0,
            output_tokens: 0,
            total_tokens: 0,
        }
    }
}

impl Default for Usage {
    fn default() -> Self {
        Self::new()
    }
}

///可累加语法糖
impl Add for Usage {
    type Output = Self;

    fn add(self, other: Self) -> Self::Output {
        Self {
            input_tokens: self.input_tokens + other.input_tokens,
            output_tokens: self.output_tokens + other.output_tokens,
            total_tokens: self.total_tokens + other.total_tokens,
        }
    }
}

///可累加语法糖
impl AddAssign for Usage {
    fn add_assign(&mut self, other: Self) {
        self.input_tokens += other.input_tokens;
        self.output_tokens += other.output_tokens;
        self.total_tokens += other.total_tokens;
    }
}
