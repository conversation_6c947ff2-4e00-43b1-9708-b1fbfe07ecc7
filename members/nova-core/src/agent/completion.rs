use std::sync::Arc;

use crate::completion::Completion;
use crate::completion::CompletionError;
use crate::completion::CompletionModel;
use crate::completion::CompletionRequestBuilder;
use crate::completion::Document;
use crate::completion::Message;

#[derive(Clone)]
#[non_exhaustive]
pub struct Agent<M>
where
    M: CompletionModel,
{
    /// Name of the agent used for logging and debugging
    pub name: Option<String>,
    /// Completion model (e.g.: OpenAI's gpt-3.5-turbo-1106, Cohere's command-r)
    pub model: Arc<M>,
    /// System prompt
    pub preamble: Option<String>,
    /// Context documents always available to the agent
    pub static_context: Vec<Document>,
    /// Tools that are always available to the agent (identified by their name)
    pub static_tools: Vec<String>,
    /// Temperature of the model
    pub temperature: Option<f64>,
    /// Maximum number of tokens for the completion
    pub max_tokens: Option<u64>,
    /// Additional parameters to be passed to the model
    pub additional_params: Option<serde_json::Value>,
    // /// List of vector store, with the sample number
    // pub dynamic_context: Arc<Vec<(usize, Box<dyn crate::vector_store::VectorStoreIndexDyn>)>>,
    // /// Dynamic tools
    // pub dynamic_tools: Arc<Vec<(usize, Box<dyn crate::vector_store::VectorStoreIndexDyn>)>>,
    // /// Actual tool implementations
    // pub tools: Arc<ToolSet>,
}

impl<M> Completion<M> for Agent<M>
where
    M: CompletionModel,
{
    // #[tracing::instrument(skip(self, prompt, chat_history), fields(agent_name = self.name()))]
    async fn completion(
        &self,
        prompt: impl Into<Message> + Send,
        chat_history: Vec<Message>,
    ) -> Result<CompletionRequestBuilder<M>, CompletionError> {
        let prompt = prompt.into();

        // Find the latest message in the chat history that contains RAG text
        let rag_text = prompt.rag_text();
        let rag_text = rag_text.or_else(|| {
            chat_history
                .iter()
                .rev()
                .find_map(|message| message.rag_text())
        });

        let completion_request = self
            .model
            .completion_request(prompt)
            .messages(chat_history)
            .temperature_opt(self.temperature)
            .max_tokens_opt(self.max_tokens)
            .additional_params_opt(self.additional_params.clone());
        let completion_request = if let Some(preamble) = &self.preamble {
            completion_request.preamble(preamble.to_owned())
        } else {
            completion_request
        };

        // If the agent has RAG text, we need to fetch the dynamic context and tools
        let agent = match &rag_text {
            Some(text) => {
                // let dynamic_context = stream::iter(self.dynamic_context.iter())
                //     .then(|(num_sample, index)| async {
                //         let req = VectorSearchRequest::builder().query(text).samples(*num_sample
                // as u64).build().expect("Creating VectorSearchRequest here shouldn't fail since
                // the query and samples to return are always present");
                //         Ok::<_, VectorStoreError>(
                //             index
                //                 .top_n(req)
                //                 .await?
                //                 .into_iter()
                //                 .map(|(_, id, doc)| {
                //                     // Pretty print the document if possible for better
                // readability                     let text =
                // serde_json::to_string_pretty(&doc)                         
                // .unwrap_or_else(|_| doc.to_string());
                //
                //                     Document {
                //                         id,
                //                         text,
                //                         additional_props: HashMap::new(),
                //                     }
                //                 })
                //                 .collect::<Vec<_>>(),
                //         )
                //     })
                //     .try_fold(vec![], |mut acc, docs| async {
                //         acc.extend(docs);
                //         Ok(acc)
                //     })
                //     .await
                //     .map_err(|e| CompletionError::RequestError(Box::new(e)))?;
                //
                // let dynamic_tools = stream::iter(self.dynamic_tools.iter())
                //     .then(|(num_sample, index)| async {
                //         let req = VectorSearchRequest::builder().query(text).samples(*num_sample
                // as u64).build().expect("Creating VectorSearchRequest here shouldn't fail since
                // the query and samples to return are always present");
                //         Ok::<_, VectorStoreError>(
                //             index
                //                 .top_n_ids(req)
                //                 .await?
                //                 .into_iter()
                //                 .map(|(_, id)| id)
                //                 .collect::<Vec<_>>(),
                //         )
                //     })
                //     .try_fold(vec![], |mut acc, docs| async {
                //         for doc in docs {
                //             if let Some(tool) = self.tools.get(&doc) {
                //                 acc.push(tool.definition(text.into()).await)
                //             } else {
                //                 tracing::warn!("Tool implementation not found in toolset: {}",
                // doc);             }
                //         }
                //         Ok(acc)
                //     })
                //     .await
                //     .map_err(|e| CompletionError::RequestError(Box::new(e)))?;
                //
                // let static_tools = stream::iter(self.static_tools.iter())
                //     .filter_map(|toolname| async move {
                //         if let Some(tool) = self.tools.get(toolname) {
                //             Some(tool.definition(text.into()).await)
                //         } else {
                //             tracing::warn!(
                //                 "Tool implementation not found in toolset: {}",
                //                 toolname
                //             );
                //             None
                //         }
                //     })
                //     .collect::<Vec<_>>()
                //     .await;
                //
                // completion_request
                //     .documents(dynamic_context)
                //     .tools([static_tools.clone(), dynamic_tools].concat())
                completion_request.preamble("".to_owned())
            }
            None => {
                // let static_tools = stream::iter(self.static_tools.iter())
                //     .filter_map(|toolname| async move {
                //         if let Some(tool) = self.tools.get(toolname) {
                //             // TODO: tool definitions should likely take an `Option<String>`
                //             Some(tool.definition("".into()).await)
                //         } else {
                //             tracing::warn!(
                //                 "Tool implementation not found in toolset: {}",
                //                 toolname
                //             );
                //             None
                //         }
                //     })
                //     .collect::<Vec<_>>()
                //     .await;

                // completion_request.tools(static_tools)
                completion_request.preamble("".to_owned())
            }
        };

        Ok(agent)
    }
}
