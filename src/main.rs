use std::any::type_name;
use std::io::Error;
use std::time::Duration;
use anyhow::Result;
use dotenvy::dotenv;
use envconfig::Envconfig;
use nova_lab::{get_subscriber, send_email, Email};
use nova_lab::init_subscriber;
use nova_lab::OtlGuard;
use nova_lab::Server;
use nova_lab::{shutdown, NovaConfig};
use nova_runtime::prelude::*;
use nova_runtime_redis::{create_redis_script, RedisScript, RedisStorage};
use serde::{Deserialize, Serialize};
use tokio::io;
use tracing::{info, error};
use tracing_subscriber::util::SubscriberInitExt;
use fred::{
    prelude::*,
    types::scripts::{Library, Script},
    util as fred_utils,
};
use nova_runtime::worker::WorkerConfig;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    let config = NovaConfig::init_from_env()?;
    let _guard = OtlGuard {
        telemetry_url: config.trace.telemetry_endpoint.clone(),
    };

    let subscriber = get_subscriber(
        "nova-server".into(),
        config.trace.server_log_level.clone(),
        std::io::stdout,
        config.trace.telemetry_endpoint.clone(),
    );
    init_subscriber(subscriber);
    info!("Starting Nova-server with config: {:?}", config);

    // // 启动工作线程
    // let queue_manager = server.state.queue_manager.clone();
    // let worker_tasks = (0..3).map(|i| {
    //     let worker = Worker::new(format!("worker-{}", i), queue_manager.clone());
    //     tokio::spawn(async move {
    //         worker.start().await;
    //     })
    // }).collect::<Vec<_>>();
    //
    // info!("Started {} worker threads", worker_tasks.len());
    let (shutdown_tx, _) = tokio::sync::broadcast::channel(1);

    // Redis 连接配置
    let redis_config = Config::from_url(&config.redis_url)?;
    let client = Builder::from_config(redis_config)
        .with_connection_config(|redis_config| {
            redis_config.connection_timeout = Duration::from_secs(5);
            redis_config.tcp = TcpConfig {
                nodelay: Some(true),
                ..Default::default()
            };
        })
        .set_policy(ReconnectPolicy::new_exponential(0, 100, 30_000, 2))
        .build()?;
    client.init().await?;

    client.on_error(|(error, server)| async move {
        error!("{:?}: redis Connection  lost to: {:?}", server, error);
        Ok(())
    });
    info!("Redis client created successfully");
    let redis_script = create_redis_script(&client).await?;
    // 创建 RedisStorage，内部会自动验证连接
    let storage: RedisStorage<Email> = RedisStorage::new(client.clone(), redis_script).await;

    let axum_shutdown_tx = shutdown_tx.clone();
    let server = Server::init(config, storage.clone()).await;
    let http = async {
        server
            .run(axum_shutdown_tx)
            .await
            .map_err(|e| Error::new(std::io::ErrorKind::Interrupted, e))
    };

    let task_shutdown_tx = shutdown_tx.clone();
    let worker_config = WorkerConfig::default();
    let task_coordinator = async {
        Coordinator::new()
            .register({
                WorkerBuilder::new("test",worker_config)
                    .catch_panic()
                    // .enable_tracing()
                    .mq(storage.clone())
                    .build_fn(send_email)
            })
            .on_event(|event| {
                info!("Worker event: {:?}", event);
            })
            .run_with_signal(async {
                let _ = task_shutdown_tx.subscribe().recv().await;
                Ok(())
            })
            // .shutdown_timeout(Duration::from_secs(10))
            // .on_event(|event| {
            //     info!("Worker event: {:?}", event);
            // })
            .await
            .unwrap();
        Ok::<(), io::Error>(())
    };
    let _res = tokio::join!(http,task_coordinator, shutdown(shutdown_tx));
    Ok(())
}


