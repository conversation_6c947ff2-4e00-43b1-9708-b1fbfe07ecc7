use std::fmt::Debug;
use std::sync::Arc;
use axum::response::{Html, IntoResponse};
use axum::{Form, Json, Router};
use axum::extract::State;
use axum::routing::{get, post};
use http::StatusCode;
use serde::de::DeserializeOwned;
use serde::Serialize;
use serde_json::json;
use tower_http::cors::CorsLayer;
use nova_runtime::Storage;
use nova_runtime_redis::RedisContext;
use crate::{AppState, Email, ServerResponse};

pub mod queue;

pub async fn get_router(state: Arc<AppState>) -> Router<Arc<AppState>> {
    // let path = format!("/{}", state.config.api_version);
    // let public_path = format!("{path}/public");
    Router::new()
        .route("/add", post(add_new_job))
        .with_state(state)
        // .nest("/api/v1/queue", queue::get_queue_router())
        // .nest(&public_path, public::get_router(state))
        // .nest(&path, secured_key::get_router(state).await)
        // .nest(&path, secured_jwt::get_router(state).await)
        // .route("/", get(get_root))
        .fallback(not_found_handler)
        .layer(CorsLayer::permissive())
}

pub async fn not_found_handler() -> impl IntoResponse {
    (
        StatusCode::NOT_FOUND,
        Json(ServerResponse {
            response_type: "error".to_string(),
            args: json!({
                "error": "Not found", // For backward compatibility
            }),
        }),
    )
}




async fn add_new_job(
    State(state): State<Arc<AppState>>,
    Json(input): Json<Email>,
) -> impl IntoResponse
{
    let mut storage = state.redis_storage.clone();
    let worker_id = "test".to_string();
    let new_job = storage.push_with_ctx(input,RedisContext::new(Some(worker_id))).await;

    match new_job {
        Ok(ctx) => (
            StatusCode::CREATED,
            format!("Job [{ctx:?}] was successfully added"),
        ),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            format!("An Error occurred {e}"),
        ),
    }
        .into_response()
}
